import { createRouter, createWebHashHistory } from 'vue-router'
import MainLayout from '../views/MainLayout.vue'
import Dashboard from '../components/Dashboard.vue'
import FileBrowser from '../components/FileBrowser.vue'
import BatchRename from '../components/BatchRename.vue'
import FileTransfer from '../components/FileTransfer.vue'
import AutoSync from '../components/AutoSync.vue'
import AdCleaner from '../components/AdCleaner.vue'
import RandomInsert from '../components/RandomInsert.vue'
import BatchShare from '../components/BatchShare.vue'
import LinkConvert from '../components/LinkConvert.vue'
import Settings from '../components/Settings.vue'
import QuarkDownloadTest from '../components/QuarkDownloadTest.vue'
import BaiduUploadSimple from '../components/BaiduUploadSimple.vue'


import { setupRouterGuards } from './guards'

const routes = [
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard
      },
      {
        path: 'download-test',
        name: 'QuarkDownloadTest',
        component: QuarkDownloadTest
      },
      {
        path: 'upload-test',
        name: 'BaiduUploadSimple',
        component: BaiduUploadSimple
      },

      {
        path: 'files',
        name: 'FileBrowser',
        component: FileBrowser
      },
      {
        path: 'rename',
        name: 'BatchRename',
        component: BatchRename
      },
      {
        path: 'transfer',
        name: 'FileTransfer',
        component: FileTransfer
      },

      {
        path: 'sync',
        name: 'AutoSync',
        component: AutoSync
      },
      {
        path: 'clean',
        name: 'AdCleaner',
        component: AdCleaner
      },
      {
        path: 'random-insert',
        name: 'RandomInsert',
        component: RandomInsert
      },
      {
        path: 'batch-share',
        name: 'BatchShare',
        component: BatchShare
      },
      {
        path: 'link-convert',
        name: 'LinkConvert',
        component: LinkConvert
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 设置路由守卫
setupRouterGuards(router)

export default router
