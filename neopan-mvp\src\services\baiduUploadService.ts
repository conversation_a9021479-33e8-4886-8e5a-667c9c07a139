import { TauriHttpClient } from './httpClient'

export interface BaiduUploadProgress {
  uploaded: number
  total: number
  percentage: number
  speed: number
  speedText: string
}

export interface BaiduUploadResult {
  success: boolean
  message: string
  fileId?: string
  path?: string
}

export interface BaiduFileInfo {
  fs_id: number
  path: string
  server_filename: string
  size: number
  isdir: number
  category: number
  md5?: string
}

export class BaiduUploadService {
  private httpClient: TauriHttpClient
  private cookie: string
  private bdstoken: string | null = null

  constructor(_cookie: string) {
    // 临时硬编码Cookie用于测试 - 统一使用这个Cookie进行所有上传行为
    const hardcodedCookie = 'XFI=35229335-ad2c-ae53-4b98-13a75cbc7374; XFCS=42A03FF5771E92D3DD98680615EBE9421F1C4F8F40D22028CA6637E3C4621929; XFT=jIXBuWOZ1T21vUHHXCUl9d6V0epfBtcJ1A/lK8nqRk8=; csrfToken=W23rb3vs5_LgPiRMAlnhQ87g; PSTM=1738467890; BIDUPSID=5F2AB08908E9B3D603DCB1AAFD638CE4; BDSFRCVID=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; HMACCOUNT=E06BDF8E3EA1166E; PANWEB=1; Hm_lpvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; Hm_lvt_95fc87a381fad8fcb37d76ac51fefcea=**********,**********; Hm_lpvt_95fc87a381fad8fcb37d76ac51fefcea=**********; XFI=f82a60a0-1881-11f0-96de-ddea43cfd8b4; XFCS=DACD04E678A6B6FBAAC9EB2A0447F305B9C6026C72BA17195959F355364A2541; XFT=UyFoh0F56M4AIeSuLoPWo5QWRKf13iDeR1RwIG1YnTg=; BDUSS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; BDUSS_BFESS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; H_WISE_SIDS_BFESS=110085_626068_628198_632156_633618_632291_632300_641765_642956_643573_641767_644369_644665_644645_645089_645170_645697_645921_644402_646540_646556_646774_646740_645030_647625_647664_647709_647691_647902_647927_648250_648403_648453_648452_648467_648464_648471_648479_648450_648500_648474_648498_648477_648505_648502_648506_648473_648461_648432_648442_648590_648725_648743_649009_649056_649073_649065_649050_649018_648986_648587_648094_649186_649234_646542_649356_649344_641262_649532_649589_649659_649650_649760_649713_649751_649775_649809_649869_649871_649778_649910_649230_649928_649959_649936_649920_650034_650069_650049_650041_639680_649885_644053_650204_650215_650210_650218_650084_650270_650259_650262_650288_650285_650312_650329_650324_650323_650328_650420; MCITY=-119%3A; Hm_lvt_fa0277816200010a74ab7d2895df481b=1750308228; Hm_lpvt_fa0277816200010a74ab7d2895df481b=1750657333; Hm_lvt_d5bdc9eee733100f7b952fc44f7e53e4=1749284833,1751556345; Hm_lpvt_d5bdc9eee733100f7b952fc44f7e53e4=1751598458; Hm_lvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1750215916,1752144625; Hm_lpvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1752144625; BAIDUID=B6C9C93D66164CBA01B02AED75FF179B:FG=1; newlogin=1; BAIDUID_BFESS=B6C9C93D66164CBA01B02AED75FF179B:FG=1; BDSFRCVID_BFESS=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF_BFESS=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; delPer=0; PSINO=7; ZFY=3nckonzJ6aE80MKyUr4y8zQoAPXHmiqyK35M1BfC5XY:C; BDCLND=tE3Nof7QnlSVboq1mb1gUQnAYUJV0sXIZ7bpW714pVE%3D; STOKEN=424372d096960b2d01ce2b9e63e4e05ac1302157657cc94cacdb6c903accb967; H_PS_PSSID=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; H_WISE_SIDS=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; Hm_lvt_182d6d59474cf78db37e0b2248640ea5=1753414973; Hm_lpvt_182d6d59474cf78db37e0b2248640ea5=1753553612; ndut_fmt=C43E2B2038A6168778CCFA43292A4BD592ADBFBC98D2B62722F4AA2992B3EE66; ab_sr=1.0.1_NTI1ZDhhZGExN2U2ZjNjZGUyZDUwNjc3Yzg2YzQ0MjFkMGUxY2Q1MzFlYmJkYWFhN2E0ZjIzY2M1YmQwMDBiYzllNTZmNTI3MTRkNzU5NjYxYzY3NDAzY2UyMjRlNDJlZWM1YTBiNjIxZjgwNWJhNzE4MDBjNDRlYzc0MTY1NTQwM2M4MDhhY2VlN2E2NzEwZjNiNGFmZmViMzliMTVkMjg5MTZkMmRiZTA4NzJmZmU5NTNhZmE5YTViZTM2YWUy; PANPSC=7818778965945482978%3AnHZOtuqy9avxzjnZjz4PEFcS2d9ns3O5C61tf8CKQkjghmkhEDEibA3MPJHnDhkCz81ttRoL0tCAzpMjKfHvSlRJmYG59WfnDmIVxiIPZ%2BhtcQe%2BbgwEaTCyYQOwAMBA4CvFohGLzm4xCCiUPpEZ9rj2cUzyGc10baC532AuNi3w0iRW66wpccQp2WmIaeM4j9JYwYAV4WIE%2BW3bn8WQpiM3H8XB9kjAw%2FFO59HDGjBoPH46caSWouLaMusRzMQHj%2FrJb%2BRIEq3GB1Ggo%2FVEtCYpmymoXX%2BPeeyDNZbt4BHtb5iP4kyX6ZKxVlqrIDxM'

    // 使用硬编码Cookie，忽略传入的cookie参数
    this.cookie = hardcodedCookie
    this.httpClient = new TauriHttpClient()

    console.log('🔍 [BaiduUpload] 使用硬编码测试Cookie，长度:', hardcodedCookie.length)
  }

  /**
   * 获取bdstoken
   */
  private async getBdstoken(): Promise<string | null> {
    if (this.bdstoken) {
      console.log('🔍 [BaiduUpload] 使用缓存的bdstoken:', this.bdstoken)
      return this.bdstoken
    }

    try {
      console.log('🔍 [BaiduUpload] 开始获取bdstoken...')
      console.log('🔍 [BaiduUpload] Cookie长度:', this.cookie.length)

      // 使用Tauri后端获取bdstoken，完全按照Python脚本方式
      const { invoke } = await import('@tauri-apps/api/core')

      const response = await invoke('get_baidu_bdstoken', {
        cookie: this.cookie
      }) as any

      console.log('🔍 [BaiduUpload] bdstoken响应:', response)

      if (response && response.errno === 0 && response.result && response.result.bdstoken) {
        this.bdstoken = response.result.bdstoken
        console.log('🔍 [BaiduUpload] 获取bdstoken成功:', this.bdstoken)
        return this.bdstoken
      } else {
        console.error('🔍 [BaiduUpload] bdstoken获取失败:', response)
      }

      console.error('🔍 [BaiduUpload] 获取bdstoken失败 - 响应无效')

      // 尝试备用方案：从Cookie中提取
      console.log('🔍 [BaiduUpload] 尝试从Cookie中提取bdstoken...')

      // 检查Cookie中是否包含bdstoken
      const cookieMatch = this.cookie.match(/bdstoken=([^;]+)/)
      if (cookieMatch) {
        this.bdstoken = cookieMatch[1]
        console.log('🔍 [BaiduUpload] 从Cookie中提取bdstoken:', this.bdstoken)
        return this.bdstoken
      }

      // 如果Cookie中也没有，抛出错误而不是使用默认值
      throw new Error('无法获取有效的bdstoken，请检查百度网盘Cookie是否正确')

    } catch (error) {
      console.error('🔍 [BaiduUpload] 获取bdstoken异常:', error)

      // 异常情况下尝试从Cookie提取
      const cookieMatch = this.cookie.match(/bdstoken=([^;]+)/)
      if (cookieMatch) {
        this.bdstoken = cookieMatch[1]
        console.log('🔍 [BaiduUpload] 异常情况从Cookie中提取bdstoken:', this.bdstoken)
        return this.bdstoken
      }

      // 如果都失败了，抛出错误
      throw new Error(`获取bdstoken失败: ${error}`)
    }
  }

  /**
   * 计算文件MD5（使用Tauri后端）
   */
  private async calculateMD5(file: File): Promise<string> {
    try {
      const { invoke } = await import('@tauri-apps/api/core')

      // 将文件转换为字节数组
      const arrayBuffer = await file.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // 调用Tauri后端计算MD5
      const md5 = await invoke('calculate_file_md5', {
        fileData: Array.from(uint8Array)
      }) as string

      return md5
    } catch (error) {
      console.error('🔍 [BaiduUpload] 计算MD5失败:', error)
      // 如果MD5计算失败，使用文件大小和时间戳作为替代
      const fallbackHash = `${file.size}_${file.lastModified}_${Date.now()}`
      return btoa(fallbackHash).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32).toLowerCase()
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 检查Cookie有效性
   */
  async checkCookie(): Promise<boolean> {
    try {
      console.log('🔍 [BaiduUpload] 开始验证Cookie...')
      console.log('🔍 [BaiduUpload] Cookie长度:', this.cookie.length)
      console.log('🔍 [BaiduUpload] Cookie前100字符:', this.cookie.substring(0, 100))

      // 简单检查Cookie格式
      if (!this.cookie || this.cookie.trim().length < 10) {
        console.log('🔍 [BaiduUpload] Cookie格式无效')
        return false
      }

      // 检查是否包含百度网盘相关的Cookie字段
      const hasBDUSS = this.cookie.includes('BDUSS')
      const hasSTOKEN = this.cookie.includes('STOKEN')
      const hasBaiduId = this.cookie.includes('BAIDUID')

      console.log('🔍 [BaiduUpload] Cookie检查:')
      console.log('🔍 [BaiduUpload] - 包含BDUSS:', hasBDUSS)
      console.log('🔍 [BaiduUpload] - 包含STOKEN:', hasSTOKEN)
      console.log('🔍 [BaiduUpload] - 包含BAIDUID:', hasBaiduId)

      // 百度网盘Cookie必须包含BDUSS和STOKEN
      const isValidBaiduCookie = hasBDUSS && hasSTOKEN

      if (!isValidBaiduCookie) {
        console.error('🔍 [BaiduUpload] Cookie无效: 缺少必要的BDUSS或STOKEN字段')
        console.log('🔍 [BaiduUpload] 请确保Cookie包含完整的百度网盘登录信息')
      }

      return isValidBaiduCookie

    } catch (error) {
      console.error('🔍 [BaiduUpload] 检查百度网盘Cookie失败:', error)
      return false
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<any> {
    try {
      console.log('🔍 [BaiduUpload] 获取用户信息...')

      // 暂时返回模拟数据，避免API调用问题
      return {
        used: 1024 * 1024 * 1024, // 1GB
        total: 1024 * 1024 * 1024 * 1024, // 1TB
        free: 1024 * 1024 * 1024 * 1023 // 1023GB
      }
    } catch (error) {
      console.error('🔍 [BaiduUpload] 获取百度网盘用户信息失败:', error)
      throw error
    }
  }

  /**
   * 预上传 - 获取上传参数（按照Python脚本实现）
   */
  private async precreateUpload(file: File, targetPath: string): Promise<any> {
    try {
      console.log('🔍 [BaiduUpload] 步骤1: 预创建上传任务...')

      const bdstoken = await this.getBdstoken()
      if (!bdstoken) {
        throw new Error('获取bdstoken失败')
      }

      // 🚀 按照抓包数据：先计算所有分片的MD5
      const fileSize = file.size
      const fileName = file.name
      const fileMtime = Math.floor(Date.now() / 1000)

      // 构建上传路径
      let uploadPath = targetPath.replace(/\/+$/, '') + '/' + fileName
      if (uploadPath.startsWith('//')) {
        uploadPath = uploadPath.substring(1)
      }

      // 计算分片信息
      const CHUNK_SIZE = 33554432 // 32MB，按照抓包数据
      const totalChunks = Math.ceil(fileSize / CHUNK_SIZE)

      console.log('🔍 [BaiduUpload] 文件信息:')
      console.log('🔍 [BaiduUpload] - 文件名:', fileName)
      console.log('🔍 [BaiduUpload] - 文件大小:', fileSize)
      console.log('🔍 [BaiduUpload] - 分片数量:', totalChunks)
      console.log('🔍 [BaiduUpload] - 上传路径:', uploadPath)

      // 🚀 关键：计算所有分片的MD5
      console.log('🔍 [BaiduUpload] 开始计算所有分片的MD5...')
      const chunkMD5List: string[] = []

      for (let i = 0; i < totalChunks; i++) {
        const start = i * CHUNK_SIZE
        const end = Math.min(start + CHUNK_SIZE, fileSize)
        const chunk = file.slice(start, end)
        const chunkMD5 = await this.calculateMD5(chunk as File)
        chunkMD5List.push(chunkMD5)
        console.log(`🔍 [BaiduUpload] 分片 ${i + 1}/${totalChunks} MD5: ${chunkMD5}`)
      }

      console.log('🔍 [BaiduUpload] 所有分片MD5:', chunkMD5List)

      // 构建URL参数
      const urlParams = new URLSearchParams({
        bdstoken: bdstoken,
        app_id: '250528',
        channel: 'chunlei',
        web: '1',
        clienttype: '0',
        'dp-logid': String(Math.floor(Date.now() * 1000)) + '0001'
      })

      // 🚀 按照抓包数据：使用分片MD5列表
      const postData = new URLSearchParams({
        path: uploadPath,
        autoinit: '1',
        block_list: JSON.stringify(chunkMD5List), // 使用分片MD5列表
        target_path: targetPath,
        local_mtime: String(fileMtime)
      })

      console.log('🔍 [BaiduUpload] 预上传URL参数:', urlParams.toString())
      console.log('🔍 [BaiduUpload] 预上传POST数据:', postData.toString())

      // 使用Tauri后端发送请求，完全按照Python脚本的方式
      const { invoke } = await import('@tauri-apps/api/core')

      const response = await invoke('baidu_precreate_upload', {
        url: 'https://pan.baidu.com/api/precreate',
        params: Object.fromEntries(urlParams.entries()),
        data: Object.fromEntries(postData.entries()),
        cookie: this.cookie
      }) as any

      console.log('🔍 [BaiduUpload] 预上传响应:', response)

      if (response && response.errno === 0) {
        console.log('✅ 预创建成功')
        console.log('   - 文件路径:', uploadPath)
        console.log('   - 文件大小:', fileSize, 'bytes')
        console.log('   - 分片MD5列表:', chunkMD5List)
        console.log('   - UploadID:', response.uploadid)
        console.log('   - 需要上传的分片:', response.block_list)

        return {
          uploadid: response.uploadid,
          path: uploadPath,
          size: fileSize,
          chunkMD5List: chunkMD5List, // 保存分片MD5列表
          mtime: fileMtime,
          target_path: targetPath,
          block_list: response.block_list || []
        }
      } else {
        throw new Error(`预创建失败 - 错误码: ${response?.errno}, 错误信息: ${JSON.stringify(response)}`)
      }
    } catch (error) {
      console.error('🔍 [BaiduUpload] 预上传失败:', error)
      throw error
    }
  }

  /**
   * 上传文件内容（分片上传，严格按照抓包数据实现）
   */
  private async uploadFileContent(file: File, uploadInfo: any): Promise<any> {
    try {
      console.log('🔍 [BaiduUpload] 步骤2: 分片上传文件内容...')
      console.log(`📊 文件大小: ${this.formatFileSize(file.size)}`)

      const CHUNK_SIZE = 33554432 // 32MB，按照抓包数据
      const totalChunks = Math.ceil(file.size / CHUNK_SIZE)

      console.log(`📦 分片信息: ${totalChunks} 个分片，每片 ${this.formatFileSize(CHUNK_SIZE)}`)

      // 根据预创建响应的 block_list 决定需要上传哪些分片
      const needUploadChunks = uploadInfo.block_list || []
      console.log('🔍 [BaiduUpload] 需要上传的分片:', needUploadChunks)

      const chunkResults: any[] = []

      // 上传需要的分片
      for (const partseq of needUploadChunks) {
        if (partseq >= totalChunks) {
          console.warn(`⚠️ 分片序号 ${partseq} 超出范围，跳过`)
          continue
        }

        const start = partseq * CHUNK_SIZE
        const end = Math.min(start + CHUNK_SIZE, file.size)
        const chunk = file.slice(start, end)

        console.log(`� 上传分片 ${partseq + 1}/${totalChunks} (${this.formatFileSize(start)} - ${this.formatFileSize(end)})`)

        const result = await this.uploadChunk(chunk, partseq, uploadInfo)
        chunkResults[partseq] = result

        console.log(`✅ 分片 ${partseq + 1} 上传完成，MD5: ${result.md5}`)
      }

      // 收集所有分片的MD5
      const blockList = uploadInfo.chunkMD5List || chunkResults.map(result => result?.md5).filter(md5 => md5)

      console.log('✅ 所有分片上传完成')
      console.log('🔍 [BaiduUpload] 分片MD5列表:', blockList)

      return {
        block_list: blockList,
        uploadid: uploadInfo.uploadid
      }
    } catch (error) {
      console.error('🔍 [BaiduUpload] 分片上传失败:', error)
      throw error
    }
  }

  /**
   * 快速上传完成（严格按照抓包数据实现）
   */
  private async rapidUpload(uploadInfo: any, uploadResult: any): Promise<any> {
    try {
      console.log('🔍 [BaiduUpload] 步骤3: 快速上传完成...')

      const bdstoken = await this.getBdstoken()
      if (!bdstoken) {
        throw new Error('获取bdstoken失败')
      }

      // 🚀 严格按照抓包数据：使用 rapidupload API
      const urlParams = new URLSearchParams({
        rtype: '1',
        bdstoken: bdstoken,
        app_id: '250528',
        channel: 'chunlei',
        web: '1',
        clienttype: '0',
        'dp-logid': String(Math.floor(Date.now() * 1000)) + '0005'
      })

      console.log('🔍 [BaiduUpload] 快速上传URL参数:', urlParams.toString())

      // 使用Tauri后端发送GET请求
      const { invoke } = await import('@tauri-apps/api/core')

      const response = await invoke('baidu_precreate_upload', {
        url: 'https://pan.baidu.com/api/rapidupload',
        params: Object.fromEntries(urlParams.entries()),
        data: {}, // GET请求，无POST数据
        cookie: this.cookie
      }) as any

      console.log('🔍 [BaiduUpload] 快速上传响应:', response)

      if (response && response.errno === 0 && response.info) {
        console.log('✅ 文件创建成功')
        console.log('   - 文件ID:', response.info.fs_id)
        console.log('   - 文件路径:', response.info.path)
        console.log('   - 文件大小:', response.info.size, 'bytes')
        console.log('   - 文件MD5:', response.info.md5)

        return response.info
      } else {
        throw new Error(`快速上传失败 - 错误码: ${response?.errno}, 错误信息: ${JSON.stringify(response)}`)
      }
    } catch (error) {
      console.error('🔍 [BaiduUpload] 文件创建失败:', error)
      throw error
    }
  }

  /**
   * 完整的文件上传流程
   */
  async uploadFile(
    file: File,
    remotePath: string,
    overwrite: boolean = false,
    onProgress?: (progress: BaiduUploadProgress) => void
  ): Promise<BaiduUploadResult> {
    try {
      console.log('🚀 开始上传文件:', file.name)
      console.log('📁 目标路径:', remotePath)
      console.log('=' .repeat(50))

      // 步骤1: 预创建上传任务
      onProgress?.({
        uploaded: 0,
        total: file.size,
        percentage: 10,
        speed: 0,
        speedText: '准备中...'
      })

      const uploadInfo = await this.precreateUpload(file, remotePath)

      // 步骤2: 上传文件内容
      onProgress?.({
        uploaded: file.size * 0.3,
        total: file.size,
        percentage: 30,
        speed: 1024 * 1024,
        speedText: this.formatSpeed(1024 * 1024)
      })

      const uploadResult = await this.uploadFileContent(file, uploadInfo)

      // 步骤3: 创建文件记录
      onProgress?.({
        uploaded: file.size * 0.9,
        total: file.size,
        percentage: 90,
        speed: 1024 * 1024,
        speedText: this.formatSpeed(1024 * 1024)
      })

      const createResult = await this.rapidUpload(uploadInfo, uploadResult)

      // 完成
      onProgress?.({
        uploaded: file.size,
        total: file.size,
        percentage: 100,
        speed: 0,
        speedText: '完成'
      })

      console.log('🎉 文件上传完成!')
      console.log('📄 文件名:', file.name)
      console.log('📍 文件路径:', createResult.path)
      console.log('📊 文件大小:', file.size, 'bytes')

      return {
        success: true,
        message: '上传成功',
        fileId: createResult.fs_id?.toString(),
        path: createResult.path
      }

    } catch (error) {
      console.error('🔍 [BaiduUpload] 上传文件失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  /**
   * 上传单个分片（严格按照抓包数据实现）
   */
  private async uploadChunk(
    chunk: Blob,
    partseq: number,
    uploadInfo: any,
    onProgress?: (progress: { uploaded: number; total: number; speed: number }) => void
  ): Promise<any> {
    try {
      console.log(`🔍 [BaiduUpload] 开始上传分片 ${partseq}，大小: ${chunk.size} bytes`)

      const { invoke } = await import('@tauri-apps/api/core')

      // 将Blob转换为ArrayBuffer
      const arrayBuffer = await chunk.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // 🚀 严格按照抓包数据的参数格式
      const params = {
        method: 'upload',
        logid: btoa(Date.now() + '.' + Math.random()), // Base64编码的logid
        app_id: '250528',
        channel: 'chunlei', // Web版本参数
        web: '1',
        clienttype: '0',
        path: uploadInfo.path,
        uploadid: uploadInfo.uploadid,
        uploadsign: '0', // 新参数
        partseq: partseq.toString(),
        'dp-logid': String(Math.floor(Date.now() * 1000)) + '0002'
      }

      console.log(`🔍 [BaiduUpload] 分片 ${partseq} 上传参数:`, params)

      // 调用Tauri后端上传分片
      const result = await invoke('upload_baidu_file_content', {
        fileData: Array.from(uint8Array),
        params: params,
        cookie: this.cookie
      }) as any

      console.log(`🔍 [BaiduUpload] 分片 ${partseq} 上传响应:`, result)

      if (!result || !result.md5) {
        throw new Error(`分片 ${partseq} 上传失败`)
      }

      console.log(`✅ 分片 ${partseq} 上传成功，MD5: ${result.md5}`)

      // 模拟进度回调
      onProgress?.({
        uploaded: chunk.size,
        total: chunk.size,
        speed: chunk.size
      })

      return result

    } catch (error) {
      console.error(`❌ 上传分片 ${partseq} 失败:`, error)
      throw error
    }
  }

  /**
   * 创建文件
   */
  private async createFile(
    path: string,
    size: number,
    uploadid: string,
    block_list: string[]
  ): Promise<any> {
    try {
      const response = await this.httpClient.get('https://pan.baidu.com/api/create', {
        params: {
          isdir: 0,
          rtype: 1,
          path,
          size,
          uploadid,
          block_list: JSON.stringify(block_list)
        },
        headers: {
          'Cookie': this.cookie,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': 'https://pan.baidu.com/'
        }
      })

      if (response.status === 200 && response.data && response.data.errno === 0) {
        return response.data
      }
      throw new Error(`创建文件失败: ${response.data?.errmsg || '未知错误'}`)
    } catch (error) {
      console.error('创建文件失败:', error)
      throw error
    }
  }

  /**
   * 格式化速度
   */
  private formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond === 0) return '0 B/s'
    const k = 1024
    const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
    return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 列出文件夹内容
   */
  async listFiles(dir: string = '/'): Promise<BaiduFileInfo[]> {
    try {
      const response = await this.httpClient.get('https://pan.baidu.com/api/list', {
        params: {
          dir,
          order: 'time',
          desc: 1,
          showempty: 0,
          web: 1,
          page: 1,
          num: 100
        },
        headers: {
          'Cookie': this.cookie,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': 'https://pan.baidu.com/'
        }
      })

      if (response.status === 200 && response.data && response.data.errno === 0) {
        return response.data.list || []
      }
      throw new Error(`获取文件列表失败: ${response.data?.errmsg || '未知错误'}`)
    } catch (error) {
      console.error('获取百度网盘文件列表失败:', error)
      throw error
    }
  }
}
