<template>
  <div class="app-container">
    <!-- 自定义标题栏 -->
    <div class="title-bar">
      <div class="title-bar-title">NeoPan</div>
      <div class="title-bar-controls">
        <button @click="minimizeWindow">
          <i class="fas fa-minus"></i>
        </button>
        <button @click="closeWindow" class="close">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 侧边导航 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <div class="disk-switcher">
            <div
              class="disk-option quark"
              :class="{ active: authStore.selectedDiskType === 'quark' }"
              @click="authStore.switchDiskType('quark')"
            >
              <i class="fas fa-cloud"></i> 夸克网盘
            </div>
            <div
              class="disk-option baidu"
              :class="{ active: authStore.selectedDiskType === 'baidu' }"
              @click="authStore.switchDiskType('baidu')"
            >
              <i class="fas fa-cloud"></i> 百度网盘
            </div>
          </div>
        </div>

        <div class="sidebar-menu">
          <router-link
            to="/dashboard"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/dashboard' }"
          >
            <i class="fas fa-home"></i>
            <span>主页</span>
          </router-link>

          <router-link
            to="/download-test"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/download-test' }"
          >
            <i class="fas fa-download"></i>
            <span>下载测试</span>
          </router-link>

          <router-link
            to="/upload-test"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/upload-test' }"
          >
            <i class="fas fa-upload"></i>
            <span>上传测试</span>
          </router-link>

          <router-link
            to="/upload-test"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/upload-test' }"
          >
            <i class="fas fa-upload"></i>
            <span>上传测试</span>
          </router-link>

          <router-link
            to="/files"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/files' }"
          >
            <i class="fas fa-folder"></i>
            <span>文件浏览</span>
          </router-link>

          <router-link
            to="/rename"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/rename' }"
          >
            <i class="fas fa-font"></i>
            <span>批量重命名</span>
          </router-link>

          <router-link
            to="/transfer"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/transfer' }"
          >
            <i class="fas fa-exchange-alt"></i>
            <span>文件转存</span>
          </router-link>



          <router-link
            to="/link-convert"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/link-convert' }"
          >
            <i class="fas fa-link"></i>
            <span>链接转换</span>
          </router-link>

          <router-link
            to="/sync"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/sync' }"
          >
            <i class="fas fa-sync"></i>
            <span>文件夹同步</span>
          </router-link>

          <router-link
            to="/clean"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/clean' }"
          >
            <i class="fas fa-broom"></i>
            <span>广告清理</span>
          </router-link>

          <router-link
            to="/random-insert"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/random-insert' }"
          >
            <i class="fas fa-bullseye"></i>
            <span>广告插入</span>
          </router-link>

          <router-link
            to="/batch-share"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/batch-share' }"
          >
            <i class="fas fa-share-alt"></i>
            <span>批量分享</span>
          </router-link>

          <router-link
            to="/settings"
            class="sidebar-menu-item"
            :class="{ active: $route.path === '/settings' }"
          >
            <i class="fas fa-cog"></i>
            <span>设置</span>
          </router-link>
        </div>

        <!-- 会员状态栏 -->
        <MembershipStatusBar />
      </div>

      <!-- 内容区 -->
      <div class="content">
        <router-view v-slot="{ Component }">
          <KeepAlive :include="keepAliveComponents">
            <component :is="Component" />
          </KeepAlive>
        </router-view>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span>{{ statusText }}</span>
        <span class="status-separator">|</span>
        <span>QQ群：1005877878</span>
        <span class="status-separator">|</span>
        <span>官网：neopan.cn</span>
      </div>
      <div>版本 2.4.3 | 账户登录和主题设置请前往设置页面</div>
    </div>

    <!-- 悬浮消息组件 -->
    <FloatingMessage ref="floatingMessageRef" />

    <!-- 全局确认对话框组件 -->
    <GlobalConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, KeepAlive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useMembership } from '../composables/useMembership'
import { eventBus, MEMBERSHIP_EVENTS } from '../services/eventBus'
import { getCurrentWindow } from '@tauri-apps/api/window'
import MembershipStatusBar from '../components/MembershipStatusBar.vue'
import FloatingMessage from '../components/FloatingMessage.vue'
import GlobalConfirmDialog from '../components/GlobalConfirmDialog.vue'
import { messageService } from '../services/messageService'

const router = useRouter()
const authStore = useAuthStore()

// 会员系统
const { isLoggedIn: isMemberLoggedIn } = useMembership()

const floatingMessageRef = ref()

// 需要保持状态的组件列表（有长时间运行任务的组件）
const keepAliveComponents = [
  'BatchRename',
  'FileTransfer',
  'AdCleaner',
  'AutoSync',
  'RandomInsert',
  'BatchShare'
]

const isLoggedIn = computed(() => authStore.isLoggedIn)
const userInfo = computed(() => authStore.userInfo)

const statusText = computed(() => {
  // 检查会员登录状态而不是Cookie状态
  if (isMemberLoggedIn.value) {
    // 使用authStore的全局状态
    return authStore.currentDiskStatusText
  }
  return '未登录会员账号'
})

// 窗口控制
const minimizeWindow = async () => {
  try {
    const window = getCurrentWindow()
    await window.minimize()
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

const closeWindow = async () => {
  try {
    const window = getCurrentWindow()
    await window.close()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 网盘切换现在由authStore统一管理，这里不需要单独的函数

// 退出登录
const handleLogout = () => {
  authStore.logout()
  router.push('/dashboard')
}

// 监听会员登录状态变化
watch(isMemberLoggedIn, (newValue, oldValue) => {
  console.log('MainLayout: 会员登录状态变化', { from: oldValue, to: newValue })

  if (!newValue && oldValue) {
    // 刚刚登出
    console.log('MainLayout: 检测到会员登出，左侧导航栏将显示登录选项')
  } else if (newValue && !oldValue) {
    // 刚刚登录
    console.log('MainLayout: 检测到会员登录，左侧导航栏将隐藏登录选项')
  }
}, { immediate: true })

// 事件监听器
const handleMembershipLogout = () => {
  console.log('MainLayout: 收到会员登出事件，界面将更新显示登录选项')

  // 会员登出后，用户仍然可以访问所有页面，只是功能会受限
  // 不强制跳转到登录页面，让用户自由浏览
}

// 初始化
onMounted(() => {
  // 初始化消息服务
  if (floatingMessageRef.value) {
    messageService.setMessageComponent(floatingMessageRef.value)
  }

  // 注册会员系统事件监听
  eventBus.on(MEMBERSHIP_EVENTS.LOGOUT, handleMembershipLogout)

  // authStore.checkAuth()会自动恢复网盘选择状态
  authStore.checkAuth()
  // 所有用户都能访问所有页面，不强制跳转到登录页面
})

// 清理事件监听
onUnmounted(() => {
  eventBus.off(MEMBERSHIP_EVENTS.LOGOUT, handleMembershipLogout)
})
</script>

<style scoped>
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 220px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.disk-switcher {
  display: flex;
  border-radius: 0.375rem;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.disk-option {
  flex: 1;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.75rem;
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
}

.disk-option.quark.active {
  background: var(--quark-color);
  color: white;
}

.disk-option.baidu.active {
  background: var(--baidu-color);
  color: white;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all 0.2s;
}

.sidebar-menu-item:hover {
  background-color: var(--bg-tertiary);
}

.sidebar-menu-item.active {
  background-color: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.sidebar-menu-item i {
  width: 20px;
  margin-right: 0.75rem;
  text-align: center;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
}

.user-info {
  text-align: center;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
}

.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.status-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-separator {
  color: var(--border-color);
  font-weight: 300;
}
</style>
