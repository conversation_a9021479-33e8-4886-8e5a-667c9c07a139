use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{SystemTime, UNIX_EPOCH};
use std::fs;
use std::path::Path;
use std::env;
use uuid::Uuid;
use tauri::Emitter;

#[derive(Serialize, Deserialize)]
pub struct HttpRequest {
    method: String,
    url: String,
    headers: HashMap<String, String>,
    body: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct HttpResponse {
    status: u16,
    headers: HashMap<String, String>,
    body: String,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct QuarkQrSession {
    pub token: String,
    pub request_id: String,
    pub created_at: u64,
}

#[derive(Serialize, Deserialize)]
pub struct QuarkQrResponse {
    pub success: bool,
    pub message: Option<String>,
    pub token: Option<String>,
    pub session_id: Option<String>,
    pub status: Option<String>,
    pub cookies: Option<HashMap<String, String>>,
}

// 全局会话存储
lazy_static::lazy_static! {
    static ref QR_SESSIONS: Mutex<HashMap<String, QuarkQrSession>> = Mutex::new(HashMap::new());
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 文件系统操作命令
#[tauri::command]
fn get_app_directory() -> Result<String, String> {
    match env::current_exe() {
        Ok(exe_path) => {
            if let Some(parent) = exe_path.parent() {
                Ok(parent.to_string_lossy().to_string())
            } else {
                Err("无法获取应用目录".to_string())
            }
        }
        Err(e) => Err(format!("获取应用目录失败: {}", e))
    }
}

#[tauri::command]
fn create_dir_all(path: String) -> Result<(), String> {
    fs::create_dir_all(&path)
        .map_err(|e| format!("创建目录失败: {}", e))
}

#[tauri::command]
fn file_exists(path: String) -> bool {
    Path::new(&path).exists()
}

#[tauri::command]
fn get_file_size(path: String) -> Result<u64, String> {
    match fs::metadata(&path) {
        Ok(metadata) => Ok(metadata.len()),
        Err(e) => Err(format!("获取文件大小失败: {}", e))
    }
}

#[tauri::command]
fn calculate_file_md5(file_data: Vec<u8>) -> Result<String, String> {
    let digest = md5::compute(&file_data);
    Ok(format!("{:x}", digest))
}

#[tauri::command]
fn read_file_bytes(file_path: String) -> Result<Vec<u8>, String> {
    use std::fs;

    fs::read(&file_path).map_err(|e| format!("读取文件失败: {}", e))
}

#[tauri::command]
fn get_all_files_in_folder(folder_path: String) -> Result<Vec<serde_json::Value>, String> {
    use std::fs;
    use std::path::Path;

    fn collect_files(dir: &Path, base_path: &Path) -> Result<Vec<serde_json::Value>, String> {
        let mut files = Vec::new();

        let entries = fs::read_dir(dir).map_err(|e| format!("读取目录失败: {}", e))?;

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();

            if path.is_file() {
                let file_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                files.push(serde_json::json!({
                    "path": path.to_string_lossy(),
                    "name": file_name
                }));
            } else if path.is_dir() {
                // 递归处理子目录
                let mut sub_files = collect_files(&path, base_path)?;
                files.append(&mut sub_files);
            }
        }

        Ok(files)
    }

    let folder_path = Path::new(&folder_path);
    if !folder_path.exists() {
        return Err(format!("文件夹不存在: {}", folder_path.display()));
    }

    if !folder_path.is_dir() {
        return Err(format!("路径不是文件夹: {}", folder_path.display()));
    }

    collect_files(folder_path, folder_path)
}

#[tauri::command]
fn create_test_folder_structure(base_path: String) -> Result<String, String> {
    use std::fs;
    use std::io::Write;

    println!("创建测试文件夹结构: {}", base_path);

    // 创建基础目录
    let test_dir = format!("{}/测试文件夹结构", base_path);
    fs::create_dir_all(&test_dir).map_err(|e| format!("创建基础目录失败: {}", e))?;

    // 创建复杂的嵌套结构
    let folders = vec![
        "文档/工作文档/项目A",
        "文档/工作文档/项目B/子项目1",
        "文档/工作文档/项目B/子项目2",
        "文档/个人文档/学习资料",
        "文档/个人文档/生活记录/2024年",
        "图片/风景照片/旅行/北京",
        "图片/风景照片/旅行/上海",
        "图片/人物照片/家庭",
        "图片/人物照片/朋友",
        "视频/教程视频/编程",
        "视频/教程视频/设计",
        "视频/娱乐视频",
        "音频/音乐/流行",
        "音频/音乐/古典",
        "音频/播客",
        "软件/开发工具",
        "软件/办公软件",
        "备份/数据库备份/2024",
        "备份/代码备份",
        "临时文件/下载",
        "临时文件/缓存"
    ];

    // 创建所有文件夹
    for folder in &folders {
        let folder_path = format!("{}/{}", test_dir, folder);
        fs::create_dir_all(&folder_path).map_err(|e| format!("创建文件夹失败 {}: {}", folder, e))?;
    }

    // 创建各种类型的测试文件
    let files = vec![
        // 文档文件
        ("文档/工作文档/项目A/需求文档.txt", "这是项目A的需求文档\n包含详细的功能需求和技术规范\n\n1. 用户管理模块\n2. 数据分析模块\n3. 报表生成模块"),
        ("文档/工作文档/项目A/设计方案.md", "# 项目A设计方案\n\n## 架构设计\n- 前端：Vue.js\n- 后端：Node.js\n- 数据库：MySQL\n\n## 模块划分\n1. 用户模块\n2. 业务模块\n3. 系统模块"),
        ("文档/工作文档/项目B/子项目1/开发计划.txt", "子项目1开发计划\n\n第一阶段：需求分析（1周）\n第二阶段：设计开发（3周）\n第三阶段：测试部署（1周）"),
        ("文档/工作文档/项目B/子项目2/测试报告.txt", "测试报告\n\n功能测试：通过\n性能测试：通过\n安全测试：通过\n兼容性测试：通过"),
        ("文档/个人文档/学习资料/编程笔记.txt", "编程学习笔记\n\n1. JavaScript基础\n2. TypeScript进阶\n3. React框架\n4. Node.js后端"),
        ("文档/个人文档/生活记录/2024年/年度总结.txt", "2024年度总结\n\n工作方面：完成了3个重要项目\n学习方面：掌握了新的技术栈\n生活方面：保持了良好的作息"),

        // 配置文件
        ("软件/开发工具/config.json", r#"{"theme": "dark", "fontSize": 14, "autoSave": true, "extensions": ["prettier", "eslint"]}"#),
        ("软件/办公软件/settings.xml", "<?xml version=\"1.0\"?>\n<settings>\n  <language>zh-CN</language>\n  <autoUpdate>true</autoUpdate>\n</settings>"),

        // 数据文件
        ("备份/数据库备份/2024/users.sql", "-- 用户表备份\nCREATE TABLE users (\n  id INT PRIMARY KEY,\n  name VARCHAR(100),\n  email VARCHAR(100)\n);\n\nINSERT INTO users VALUES (1, '张三', '<EMAIL>');"),
        ("备份/代码备份/main.js", "// 主程序文件\nconst express = require('express');\nconst app = express();\n\napp.get('/', (req, res) => {\n  res.send('Hello World!');\n});\n\napp.listen(3000);"),

        // 日志文件
        ("临时文件/下载/download.log", "2024-01-15 10:30:00 - 开始下载文件A\n2024-01-15 10:30:15 - 文件A下载完成\n2024-01-15 10:31:00 - 开始下载文件B"),
        ("临时文件/缓存/cache.txt", "缓存数据\nkey1=value1\nkey2=value2\nkey3=value3"),

        // README文件
        ("README.md", "# 测试文件夹结构\n\n这是一个用于测试文件夹上传功能的复杂目录结构。\n\n## 目录说明\n- 文档/：各类文档文件\n- 图片/：图片文件目录\n- 视频/：视频文件目录\n- 音频/：音频文件目录\n- 软件/：软件和配置文件\n- 备份/：备份文件\n- 临时文件/：临时文件"),

        // 更多文件增加复杂度
        ("图片/风景照片/旅行/北京/天安门.txt", "天安门广场照片描述文件"),
        ("图片/风景照片/旅行/上海/外滩.txt", "外滩夜景照片描述文件"),
        ("图片/人物照片/家庭/全家福.txt", "全家福照片描述文件"),
        ("视频/教程视频/编程/JavaScript教程.txt", "JavaScript编程教程视频描述"),
        ("视频/教程视频/设计/UI设计.txt", "UI设计教程视频描述"),
        ("音频/音乐/流行/热门歌曲.txt", "热门流行歌曲列表"),
        ("音频/播客/技术播客.txt", "技术类播客节目列表"),
    ];

    // 创建所有文件
    for (file_path, content) in &files {
        let full_path = format!("{}/{}", test_dir, file_path);
        if let Some(parent) = std::path::Path::new(&full_path).parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建父目录失败: {}", e))?;
        }

        let mut file = fs::File::create(&full_path).map_err(|e| format!("创建文件失败 {}: {}", file_path, e))?;
        file.write_all(content.as_bytes()).map_err(|e| format!("写入文件失败 {}: {}", file_path, e))?;
    }

    // 创建一些大一点的文件来测试
    let large_files = vec![
        ("大文件/测试文件1MB.txt", 1024 * 1024),  // 1MB
        ("大文件/测试文件5MB.txt", 5 * 1024 * 1024),  // 5MB
    ];

    for (file_path, size) in &large_files {
        let full_path = format!("{}/{}", test_dir, file_path);
        if let Some(parent) = std::path::Path::new(&full_path).parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建父目录失败: {}", e))?;
        }

        let mut file = fs::File::create(&full_path).map_err(|e| format!("创建大文件失败 {}: {}", file_path, e))?;

        // 写入重复内容到指定大小
        let chunk = "这是测试数据，用于创建指定大小的文件。".repeat(100);
        let mut written = 0;
        while written < *size {
            let to_write = std::cmp::min(chunk.len(), *size - written);
            file.write_all(&chunk.as_bytes()[..to_write]).map_err(|e| format!("写入大文件失败: {}", e))?;
            written += to_write;
        }
    }

    println!("测试文件夹结构创建完成: {}", test_dir);
    println!("包含 {} 个子目录和 {} 个文件", folders.len(), files.len() + large_files.len());

    Ok(test_dir)
}

#[tauri::command]
fn read_file(path: String) -> Result<String, String> {
    fs::read_to_string(&path)
        .map_err(|e| format!("读取文件失败: {}", e))
}

#[tauri::command]
fn write_file(path: String, content: String) -> Result<(), String> {
    fs::write(&path, content)
        .map_err(|e| format!("写入文件失败: {}", e))
}

#[tauri::command]
async fn http_request(request: HttpRequest, preserve_raw_cookie: Option<bool>) -> Result<HttpResponse, String> {
    // 检查是否是 Quark API 请求
    let is_quark_api = request.url.contains("quark.cn");
    let preserve_cookie = preserve_raw_cookie.unwrap_or(false);

    // 创建HTTP客户端，针对 Quark API 进行特殊配置
    let client = if is_quark_api {
        reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .danger_accept_invalid_certs(false)
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?
    } else {
        reqwest::Client::builder()
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?
    };

    let mut req_builder = match request.method.as_str() {
        "GET" => client.get(&request.url),
        "POST" => client.post(&request.url),
        "PUT" => client.put(&request.url),
        "DELETE" => client.delete(&request.url),
        _ => return Err("Unsupported HTTP method".to_string()),
    };

    // 添加请求头，特别处理Cookie
    for (key, value) in request.headers {
        // 特别处理Cookie头
        let cleaned_value = if key.to_lowercase() == "cookie" {
            if preserve_cookie || is_quark_api {
                // 对于 Quark API 或明确要求保持原始格式的请求，只做最小清理
                let cleaned = value
                    .trim()
                    .replace('\n', "")
                    .replace('\r', "")
                    .replace('\t', " ");

                println!("Quark API Cookie - Original length: {}", value.len());
                println!("Quark API Cookie - Cleaned length: {}", cleaned.len());
                println!("Quark API Cookie preview: {}...", &cleaned[..cleaned.len().min(150)]);

                cleaned
            } else {
                // 常规清理
                let cleaned = value
                    .chars()
                    .filter(|c| !c.is_control())
                    .collect::<String>()
                    .trim()
                    .to_string();

                println!("Regular Cookie - Original length: {}", value.len());
                println!("Regular Cookie - Cleaned length: {}", cleaned.len());
                println!("Regular Cookie preview: {}...", &cleaned[..cleaned.len().min(100)]);

                cleaned
            }
        } else {
            value
        };

        // 尝试添加请求头
        match reqwest::header::HeaderValue::from_str(&cleaned_value) {
            Ok(header_value) => {
                req_builder = req_builder.header(&key, header_value);
                println!("Successfully added header: {}", key);
            }
            Err(e) => {
                println!("Failed to create header value for {}: {}", key, e);
                // 对于 Quark API，如果是 Cookie 头失败，尝试更激进的清理
                if key.to_lowercase() == "cookie" && is_quark_api {
                    let fallback_cookie = cleaned_value
                        .chars()
                        .filter(|c| c.is_ascii() && !c.is_control())
                        .collect::<String>();

                    match reqwest::header::HeaderValue::from_str(&fallback_cookie) {
                        Ok(fallback_value) => {
                            req_builder = req_builder.header(&key, fallback_value);
                            println!("Successfully added fallback cookie header");
                        }
                        Err(_) => {
                            println!("Even fallback cookie failed, skipping cookie header");
                        }
                    }
                } else {
                    return Err(format!("Invalid header value for {}: {}", key, e));
                }
            }
        }
    }

    // 添加请求体
    if let Some(body) = request.body {
        req_builder = req_builder.body(body);
    }

    // 发送请求
    match req_builder.send().await {
        Ok(response) => {
            let status = response.status().as_u16();
            let headers = response.headers()
                .iter()
                .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                .collect();

            // 使用 text() 方法，reqwest会自动处理内容编码
            match response.text().await {
                Ok(body) => {
                    println!("HTTP Response Status: {}", status);
                    println!("Response Body Length: {}", body.len());

                    // 安全地截取字符串，避免在UTF-8字符中间截断
                    let preview = if body.len() <= 200 {
                        body.clone()
                    } else {
                        // 找到200字节附近的安全截断点
                        let mut end = 200;
                        while end > 0 && !body.is_char_boundary(end) {
                            end -= 1;
                        }
                        body[..end].to_string()
                    };

                    println!("Response Body Preview: {}", preview);
                    Ok(HttpResponse { status, headers, body })
                },
                Err(e) => Err(format!("Failed to read response body: {}", e)),
            }
        }
        Err(e) => Err(format!("Request failed: {}", e)),
    }
}

// 夸克扫码登录 - 获取token
#[tauri::command]
async fn quark_qr_get_token() -> Result<QuarkQrResponse, String> {
    let request_id = Uuid::new_v4().to_string();
    let session_id = Uuid::new_v4().to_string();

    let client = reqwest::Client::new();
    let url = "https://uop.quark.cn/cas/ajax/getTokenForQrcodeLogin";

    let params = [
        ("client_id", "532"),
        ("v", "1.2"),
        ("request_id", &request_id),
    ];

    match client.get(url).query(&params).send().await {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    if data["status"].as_i64() == Some(2000000) {
                        if let Some(token) = data["data"]["members"]["token"].as_str() {
                            // 存储会话信息
                            let session = QuarkQrSession {
                                token: token.to_string(),
                                request_id: request_id.clone(),
                                created_at: SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_secs(),
                            };

                            if let Ok(mut sessions) = QR_SESSIONS.lock() {
                                sessions.insert(session_id.clone(), session);
                            }

                            return Ok(QuarkQrResponse {
                                success: true,
                                message: None,
                                token: Some(token.to_string()),
                                session_id: Some(session_id),
                                status: None,
                                cookies: None,
                            });
                        }
                    }

                    let message = data["message"].as_str().unwrap_or("获取token失败").to_string();
                    Ok(QuarkQrResponse {
                        success: false,
                        message: Some(message),
                        token: None,
                        session_id: None,
                        status: None,
                        cookies: None,
                    })
                }
                Err(e) => Err(format!("解析响应失败: {}", e)),
            }
        }
        Err(e) => Err(format!("请求失败: {}", e)),
    }
}

// 夸克扫码登录 - 检查状态
#[tauri::command]
async fn quark_qr_check_status(session_id: String) -> Result<QuarkQrResponse, String> {
    // 获取会话信息
    let session = {
        if let Ok(sessions) = QR_SESSIONS.lock() {
            sessions.get(&session_id).cloned()
        } else {
            return Err("无法访问会话存储".to_string());
        }
    };

    let session = match session {
        Some(s) => s,
        None => {
            return Ok(QuarkQrResponse {
                success: false,
                message: Some("无效的会话ID".to_string()),
                token: None,
                session_id: None,
                status: None,
                cookies: None,
            });
        }
    };

    let client = reqwest::Client::new();
    let url = "https://uop.quark.cn/cas/ajax/getServiceTicketByQrcodeToken";

    let params = [
        ("client_id", "532"),
        ("v", "1.2"),
        ("token", &session.token),
        ("request_id", &Uuid::new_v4().to_string()),
    ];

    match client.get(url).query(&params).send().await {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    let status_code = data["status"].as_i64().unwrap_or(0);

                    match status_code {
                        2000000 => {
                            // 登录成功
                            if let Some(ticket) = data["data"]["members"]["service_ticket"].as_str() {
                                // 获取cookies
                                match get_cookies_with_ticket(ticket).await {
                                    Ok(cookies) => {
                                        Ok(QuarkQrResponse {
                                            success: true,
                                            message: None,
                                            token: None,
                                            session_id: None,
                                            status: Some("success".to_string()),
                                            cookies: Some(cookies),
                                        })
                                    }
                                    Err(e) => {
                                        Ok(QuarkQrResponse {
                                            success: true,
                                            message: Some(format!("获取cookies失败: {}", e)),
                                            token: None,
                                            session_id: None,
                                            status: Some("success".to_string()),
                                            cookies: None,
                                        })
                                    }
                                }
                            } else {
                                Ok(QuarkQrResponse {
                                    success: true,
                                    message: Some("响应中没有service_ticket信息".to_string()),
                                    token: None,
                                    session_id: None,
                                    status: Some("unknown".to_string()),
                                    cookies: None,
                                })
                            }
                        }
                        50004001 => {
                            // 未扫描
                            Ok(QuarkQrResponse {
                                success: true,
                                message: None,
                                token: None,
                                session_id: None,
                                status: Some("waiting".to_string()),
                                cookies: None,
                            })
                        }
                        _ => {
                            // 其他状态
                            let message = data["message"].as_str().unwrap_or("未知状态").to_string();
                            Ok(QuarkQrResponse {
                                success: true,
                                message: Some(message),
                                token: None,
                                session_id: None,
                                status: Some("unknown".to_string()),
                                cookies: None,
                            })
                        }
                    }
                }
                Err(e) => Err(format!("解析响应失败: {}", e)),
            }
        }
        Err(e) => Err(format!("请求失败: {}", e)),
    }
}

// 使用ticket获取cookies
async fn get_cookies_with_ticket(ticket: &str) -> Result<HashMap<String, String>, String> {
    let client = reqwest::Client::new();

    // 设置请求头
    let mut headers = reqwest::header::HeaderMap::new();
    headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36".parse().unwrap());
    headers.insert("Accept", "application/json, text/plain, */*".parse().unwrap());
    headers.insert("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8".parse().unwrap());
    headers.insert("Referer", "https://pan.quark.cn/".parse().unwrap());

    // 1. 访问account/info接口
    let account_info_url = format!("https://pan.quark.cn/account/info?st={}&lw=scan", ticket);
    let account_resp = client
        .get(&account_info_url)
        .headers(headers.clone())
        .send()
        .await
        .map_err(|e| format!("访问account/info失败: {}", e))?;

    // 2. 访问主页面
    let pan_url = format!("https://pan.quark.cn/?ticket={}", ticket);
    let pan_resp = client
        .get(&pan_url)
        .headers(headers.clone())
        .send()
        .await
        .map_err(|e| format!("访问主页面失败: {}", e))?;

    // 3. 访问list页面
    let list_url = "https://pan.quark.cn/list#/list/all";
    let list_resp = client
        .get(list_url)
        .headers(headers.clone())
        .send()
        .await
        .map_err(|e| format!("访问list页面失败: {}", e))?;

    // 4. 访问用户状态API
    let user_state_url = "https://pan.quark.cn/api/initialize/getUserState";
    let user_state_resp = client
        .get(user_state_url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("访问用户状态API失败: {}", e))?;

    // 从响应头中提取Set-Cookie
    let mut cookies = HashMap::new();

    // 收集所有响应的Set-Cookie头
    let responses = vec![&account_resp, &pan_resp, &list_resp, &user_state_resp];

    for response in responses {
        for cookie_header in response.headers().get_all("set-cookie") {
            if let Ok(cookie_str) = cookie_header.to_str() {
                // 解析cookie字符串
                if let Some(cookie_pair) = cookie_str.split(';').next() {
                    if let Some((key, value)) = cookie_pair.split_once('=') {
                        cookies.insert(key.trim().to_string(), value.trim().to_string());
                    }
                }
            }
        }
    }

    // 如果没有获取到cookie，返回ticket信息供调试
    if cookies.is_empty() {
        cookies.insert("ticket".to_string(), ticket.to_string());
        cookies.insert("note".to_string(), "未获取到有效Cookie，但ticket可用于手动登录".to_string());
    }

    Ok(cookies)
}

// 注意：file_exists 和 create_dir_all 已在前面定义，这里移除重复定义

// 读取文本文件
#[tauri::command]
fn read_text_file(path: String) -> Result<String, String> {
    fs::read_to_string(&path).map_err(|e| format!("读取文件失败: {}", e))
}

// 写入文本文件
#[tauri::command]
fn write_text_file(path: String, content: String) -> Result<(), String> {
    fs::write(&path, content).map_err(|e| format!("写入文件失败: {}", e))
}

// 删除文件
#[tauri::command]
fn delete_file(path: String) -> Result<(), String> {
    fs::remove_file(&path).map_err(|e| format!("删除文件失败: {}", e))
}

// 获取临时目录
#[tauri::command]
fn get_temp_dir() -> Result<String, String> {
    let temp_dir = env::temp_dir();
    let app_temp_dir = temp_dir.join("neopan_transfer");

    // 确保临时目录存在
    if !app_temp_dir.exists() {
        fs::create_dir_all(&app_temp_dir)
            .map_err(|e| format!("创建临时目录失败: {}", e))?;
    }

    Ok(app_temp_dir.to_string_lossy().to_string())
}

// 列出目录内容
#[tauri::command]
fn list_directory(path: String) -> Result<Vec<String>, String> {
    let entries = fs::read_dir(&path).map_err(|e| format!("读取目录失败: {}", e))?;

    let mut files = Vec::new();
    for entry in entries {
        if let Ok(entry) = entry {
            if let Some(name) = entry.file_name().to_str() {
                files.push(name.to_string());
            }
        }
    }

    Ok(files)
}

// 下载文件（带进度回调，完全按照原脚本）
#[tauri::command]
async fn download_file_with_progress(
    url: String,
    file_path: String,
    headers: Option<HashMap<String, String>>,
    window: tauri::Window
) -> Result<bool, String> {
    use std::io::Write;
    use std::time::{Instant, Duration};

    println!("开始下载文件: {} -> {}", url, file_path);

    // 创建目录
    if let Some(parent) = std::path::Path::new(&file_path).parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }

    // 从URL中提取host
    let parsed_url = url::Url::parse(&url).map_err(|e| format!("解析URL失败: {}", e))?;
    let host = parsed_url.host_str().unwrap_or("drive-pc.quark.cn");

    // 创建HTTP客户端
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(300))
        .danger_accept_invalid_certs(false)
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    // 构建请求
    let mut request_builder = client.get(&url)
        .header("Host", host)
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.18.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch")
        .header("Accept", "*/*")
        .header("Accept-Language", "zh-CN")
        .header("Accept-Encoding", "gzip, deflate, br")
        .header("Connection", "keep-alive")
        .header("Referer", "https://pan.quark.cn/")
        .header("Origin", "https://pan.quark.cn")
        .header("Sec-Fetch-Dest", "empty")
        .header("Sec-Fetch-Mode", "cors")
        .header("Sec-Fetch-Site", "cross-site")
        .header("sec-ch-ua", "\"Not:A-Brand\";v=\"99\", \"Chromium\";v=\"112\"")
        .header("sec-ch-ua-mobile", "?0")
        .header("sec-ch-ua-platform", "\"Windows\"");

    // 添加额外的请求头
    if let Some(extra_headers) = headers {
        for (key, value) in extra_headers {
            request_builder = request_builder.header(&key, &value);
        }
    }

    // 发送请求
    let response = request_builder
        .send()
        .await
        .map_err(|e| format!("发送请求失败: {}", e))?;

    if !response.status().is_success() {
        let status = response.status();
        let body = response.text().await.unwrap_or_default();
        return Err(format!("下载失败，HTTP状态码: {}，响应内容: {}", status, body));
    }

    // 获取文件大小
    let total_size = response.content_length().unwrap_or(0);
    println!("文件大小: {} 字节", total_size);

    // 创建文件
    let mut file = fs::File::create(&file_path)
        .map_err(|e| format!("创建文件失败: {}", e))?;

    // 下载文件并发送进度事件
    let mut downloaded = 0u64;
    let mut stream = response.bytes_stream();
    let mut last_update = Instant::now();
    let mut last_downloaded = 0u64;

    use futures_util::StreamExt;

    while let Some(chunk) = stream.next().await {
        let chunk = chunk.map_err(|e| format!("读取数据失败: {}", e))?;
        file.write_all(&chunk).map_err(|e| format!("写入文件失败: {}", e))?;
        downloaded += chunk.len() as u64;

        // 每500ms发送一次进度更新（和原脚本类似的频率）
        let now = Instant::now();
        if now.duration_since(last_update) >= Duration::from_millis(500) || downloaded == total_size {
            let percentage = if total_size > 0 {
                (downloaded as f64 / total_size as f64) * 100.0
            } else {
                0.0
            };

            // 计算下载速度
            let elapsed = now.duration_since(last_update).as_secs_f64();
            let speed = if elapsed > 0.0 {
                (downloaded - last_downloaded) as f64 / elapsed
            } else {
                0.0
            };

            // 发送进度事件到前端
            let progress_data = serde_json::json!({
                "downloaded": downloaded,
                "total": total_size,
                "percentage": percentage,
                "speed": speed,
                "file_path": file_path
            });

            let _ = window.emit("download-progress", progress_data);

            last_update = now;
            last_downloaded = downloaded;
        }
    }

    println!("文件下载完成: {}", file_path);
    Ok(true)
}

// 保留简单下载作为备用
#[tauri::command]
async fn download_file(url: String, file_path: String, headers: Option<HashMap<String, String>>) -> Result<bool, String> {
    // 调用带进度的版本，但不传递window（用于向后兼容）
    use std::io::Write;

    // 创建目录
    if let Some(parent) = std::path::Path::new(&file_path).parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }

    // 从URL中提取host
    let parsed_url = url::Url::parse(&url).map_err(|e| format!("解析URL失败: {}", e))?;
    let host = parsed_url.host_str().unwrap_or("drive-pc.quark.cn");

    // 创建HTTP客户端
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(300))
        .danger_accept_invalid_certs(false)
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    // 构建请求
    let mut request_builder = client.get(&url)
        .header("Host", host)
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.18.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch")
        .header("Accept", "*/*")
        .header("Accept-Language", "zh-CN")
        .header("Accept-Encoding", "gzip, deflate, br")
        .header("Connection", "keep-alive")
        .header("Referer", "https://pan.quark.cn/")
        .header("Origin", "https://pan.quark.cn")
        .header("Sec-Fetch-Dest", "empty")
        .header("Sec-Fetch-Mode", "cors")
        .header("Sec-Fetch-Site", "cross-site")
        .header("sec-ch-ua", "\"Not:A-Brand\";v=\"99\", \"Chromium\";v=\"112\"")
        .header("sec-ch-ua-mobile", "?0")
        .header("sec-ch-ua-platform", "\"Windows\"");

    // 添加额外的请求头
    if let Some(extra_headers) = headers {
        for (key, value) in extra_headers {
            request_builder = request_builder.header(&key, &value);
        }
    }

    // 发送请求
    let response = request_builder
        .send()
        .await
        .map_err(|e| format!("发送请求失败: {}", e))?;

    if !response.status().is_success() {
        let status = response.status();
        let body = response.text().await.unwrap_or_default();
        return Err(format!("下载失败，HTTP状态码: {}，响应内容: {}", status, body));
    }

    // 获取文件大小
    let _total_size = response.content_length().unwrap_or(0);

    // 创建文件
    let mut file = fs::File::create(&file_path)
        .map_err(|e| format!("创建文件失败: {}", e))?;

    // 下载文件
    let mut _downloaded = 0u64;
    let mut stream = response.bytes_stream();

    use futures_util::StreamExt;

    while let Some(chunk) = stream.next().await {
        let chunk = chunk.map_err(|e| format!("读取数据失败: {}", e))?;
        file.write_all(&chunk).map_err(|e| format!("写入文件失败: {}", e))?;
        _downloaded += chunk.len() as u64;
    }

    Ok(true)
}

// 多线程分片下载（完全按照原脚本逻辑，带进度事件）
#[tauri::command]
async fn download_file_chunked(
    url: String,
    file_path: String,
    headers: Option<HashMap<String, String>>,
    expected_size: Option<u64>,
    window: tauri::Window
) -> Result<bool, String> {


    println!("开始分片下载文件: {} -> {}", url, file_path);

    // 创建目录
    if let Some(parent) = std::path::Path::new(&file_path).parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }

    // 从URL中提取host
    let parsed_url = url::Url::parse(&url).map_err(|e| format!("解析URL失败: {}", e))?;
    let host = parsed_url.host_str().unwrap_or("drive-pc.quark.cn");

    // 创建HTTP客户端
    let client = Arc::new(reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(300))
        .danger_accept_invalid_certs(false)
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?);

    // 构建基础请求头
    let mut base_headers = reqwest::header::HeaderMap::new();
    base_headers.insert("Host", host.parse().unwrap());
    base_headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.18.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch".parse().unwrap());
    base_headers.insert("Accept", "*/*".parse().unwrap());
    base_headers.insert("Accept-Language", "zh-CN".parse().unwrap());
    base_headers.insert("Connection", "keep-alive".parse().unwrap());
    base_headers.insert("Referer", "https://pan.quark.cn/".parse().unwrap());
    base_headers.insert("Origin", "https://pan.quark.cn".parse().unwrap());
    base_headers.insert("Sec-Fetch-Dest", "empty".parse().unwrap());
    base_headers.insert("Sec-Fetch-Mode", "cors".parse().unwrap());
    base_headers.insert("Sec-Fetch-Site", "cross-site".parse().unwrap());
    base_headers.insert("sec-ch-ua", "\"Not:A-Brand\";v=\"99\", \"Chromium\";v=\"112\"".parse().unwrap());
    base_headers.insert("sec-ch-ua-mobile", "?0".parse().unwrap());
    base_headers.insert("sec-ch-ua-platform", "\"Windows\"".parse().unwrap());

    // 添加额外的请求头
    if let Some(extra_headers) = headers {
        for (key, value) in extra_headers {
            if let (Ok(header_name), Ok(header_value)) = (key.parse::<reqwest::header::HeaderName>(), value.parse::<reqwest::header::HeaderValue>()) {
                base_headers.insert(header_name, header_value);
            }
        }
    }

    // 优先使用传入的文件大小，否则尝试HEAD请求获取
    let (total_size, is_size_known) = if let Some(size) = expected_size {
        if size > 0 {
            println!("使用传入的文件大小: {} 字节", size);
            (size, true)
        } else {
            println!("传入的文件大小为0，尝试HEAD请求获取");
            (0, false)
        }
    } else {
        // 尝试发送HEAD请求获取文件大小
        match client.head(&url)
            .headers(base_headers.clone())
            .send()
            .await {
            Ok(response) if response.status().is_success() => {
                let size = response.content_length().unwrap_or(0);
                if size > 0 {
                    println!("通过HEAD请求获取文件大小: {} 字节", size);
                    (size, true)
                } else {
                    println!("HEAD请求成功但无法获取文件大小，将动态下载");
                    (0, false)
                }
            }
        Ok(response) => {
            println!("HEAD请求失败，状态码: {}，将动态下载", response.status());
            (0, false)
        }
        Err(e) => {
            println!("HEAD请求失败: {}，将动态下载", e);
            (0, false)
        }
        }
    };

    // 不检查Range支持，直接尝试分片下载

    // 使用官方客户端的分片大小和并发数
    const CHUNK_SIZE: u64 = 20 * 1024 * 1024; // 20MB
    const MAX_WORKERS: usize = 16;
    const _BUFFER_SIZE: usize = 1024 * 1024; // 1MB buffer (保留以备将来使用)

    // 计算分片 - 无论文件大小是否已知都使用16线程分片下载
    let mut chunks = Vec::new();
    let mut start = 0u64;

    if is_size_known && total_size > 0 {
        // 文件大小已知，按实际大小分片
        while start < total_size {
            let end = std::cmp::min(start + CHUNK_SIZE - 1, total_size - 1);
            chunks.push((start, end));
            start = end + 1;
        }
        println!("文件大小已知: {} 字节，分片数量: {}", total_size, chunks.len());
    } else {
        // 文件大小未知，创建足够多的分片尝试下载
        // 假设最大可能是10GB，创建足够的分片
        let max_possible_size = 10u64 * 1024 * 1024 * 1024; // 10GB
        while start < max_possible_size {
            let end = start + CHUNK_SIZE - 1;
            chunks.push((start, end));
            start = end + 1;
        }
        println!("文件大小未知，创建 {} 个分片进行尝试", chunks.len());
    }

    println!("使用16线程分片下载，并发数: {}", MAX_WORKERS);

    // 创建空文件，使用按需扩展策略（避免大文件预分配卡顿）
    {
        fs::File::create(&file_path)
            .map_err(|e| format!("创建文件失败: {}", e))?;
    }

    let file_path_arc = Arc::new(file_path.clone());
    let window_arc = Arc::new(window);
    let downloaded_bytes = Arc::new(std::sync::atomic::AtomicU64::new(0));
    let start_time = std::time::Instant::now();
    let url_arc = Arc::new(url);
    let base_headers_arc = Arc::new(base_headers);

    // 重置分片计数器并显示开始信息
    static CHUNK_COUNTER: std::sync::atomic::AtomicUsize = std::sync::atomic::AtomicUsize::new(0);
    CHUNK_COUNTER.store(0, std::sync::atomic::Ordering::Relaxed);

    println!("开始多线程分片下载，总分片数: {}", chunks.len());

    // 使用信号量控制并发数
    let semaphore = Arc::new(tokio::sync::Semaphore::new(MAX_WORKERS));
    let mut tasks = Vec::new();

    for (start, end) in chunks {
        let client = client.clone();
        let url = url_arc.clone();
        let file_path = file_path_arc.clone();
        let headers = base_headers_arc.clone();
        let semaphore = semaphore.clone();
        let window = window_arc.clone();
        let downloaded_bytes = downloaded_bytes.clone();

        let task = tokio::spawn(async move {
            let _permit = semaphore.acquire().await.unwrap();
            download_chunk_with_progress(
                client,
                url,
                file_path,
                headers,
                start,
                end,
                window,
                downloaded_bytes,
                total_size,
                start_time,
                is_size_known
            ).await
        });

        tasks.push(task);
    }

    // 等待所有分片下载完成
    let mut failed_chunks = Vec::new();
    let mut range_error_chunks = 0;

    for (i, task) in tasks.into_iter().enumerate() {
        match task.await {
            Ok(Ok(_)) => {
                // 分片下载完成（静默）
            }
            Ok(Err(e)) => {
                // 检查是否是范围错误（文件大小未知时的正常情况）
                if e.contains("超出文件范围") {
                    println!("分片 {} 超出文件范围，跳过", i + 1);
                    range_error_chunks += 1;
                } else {
                    println!("分片 {} 下载失败: {}", i + 1, e);
                    failed_chunks.push(i);
                }
            }
            Err(e) => {
                println!("分片 {} 任务失败: {}", i + 1, e);
                failed_chunks.push(i);
            }
        }
    }

    // 只有在有真正的失败分片时才报告错误
    if !failed_chunks.is_empty() {
        println!("有 {} 个分片下载失败，{} 个分片超出范围", failed_chunks.len(), range_error_chunks);
        // 这里可以添加重试逻辑，但暂时不阻止下载完成
    } else if range_error_chunks > 0 {
        println!("有 {} 个分片超出文件范围，这是正常的", range_error_chunks);
    }

    println!("分片下载完成: {}", file_path);
    Ok(true)
}

// 下载单个分片（带进度更新）
async fn download_chunk_with_progress(
    client: Arc<reqwest::Client>,
    url: Arc<String>,
    file_path: Arc<String>,
    base_headers: Arc<reqwest::header::HeaderMap>,
    start: u64,
    end: u64,
    window: Arc<tauri::Window>,
    downloaded_bytes: Arc<std::sync::atomic::AtomicU64>,
    total_size: u64,
    start_time: std::time::Instant,
    is_size_known: bool,
) -> Result<(), String> {


    const _BUFFER_SIZE: usize = 1024 * 1024; // 1MB buffer (保留以备将来使用)
    const MAX_RETRIES: usize = 3;

    for retry in 0..MAX_RETRIES {
        match download_chunk_attempt_with_progress(
            &client,
            &url,
            &file_path,
            &base_headers,
            start,
            end,
            &window,
            &downloaded_bytes,
            total_size,
            start_time,
            is_size_known
        ).await {
            Ok(_) => {
                return Ok(());
            }
            Err(e) => {
                // 显示重试信息，但简化412错误
                if e.contains("412") {
                    // 412错误很常见，只在最后一次失败时显示
                    if retry == MAX_RETRIES - 1 {
                        println!("分片超出文件范围，跳过");
                    }
                } else {
                    // 其他错误显示详细信息
                    println!("分片 {}-{} 第{}次重试: {}", start, end, retry + 1, e);
                }

                if retry == MAX_RETRIES - 1 {
                    return Err(format!("分片 {}-{} 下载失败，已重试{}次", start, end, MAX_RETRIES));
                }
                // 等待一段时间再重试
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
            }
        }
    }

    Ok(())
}

// 单次分片下载尝试（带进度更新）
async fn download_chunk_attempt_with_progress(
    client: &reqwest::Client,
    url: &str,
    file_path: &str,
    base_headers: &reqwest::header::HeaderMap,
    start: u64,
    end: u64,
    window: &tauri::Window,
    downloaded_bytes: &std::sync::atomic::AtomicU64,
    total_size: u64,
    start_time: std::time::Instant,
    is_size_known: bool,
) -> Result<(), String> {
    use std::io::{Write, Seek, SeekFrom};
    use futures_util::StreamExt;

    // 创建Range请求头
    let mut headers = base_headers.clone();
    headers.insert(
        "Range",
        format!("bytes={}-{}", start, end).parse()
            .map_err(|e| format!("创建Range头失败: {}", e))?
    );

    // 发送请求
    let response = client.get(url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("发送分片请求失败: {}", e))?;

    // 检查状态码（206 Partial Content、200 OK 或 416 Range Not Satisfiable）
    if response.status() == 416 {
        // 416表示请求的范围超出了文件大小，这在文件大小未知时是正常的
        return Ok(());
    }

    if !response.status().is_success() && response.status() != 206 {
        // 412错误通常表示Range请求超出文件大小，这是正常的（文件可能比预期小）
        if response.status() == 412 {
            // 静默跳过这个分片，不视为错误
            return Ok(());
        }
        return Err(format!("分片请求失败，状态码: {}", response.status()));
    }

    // 打开文件并按需扩展
    let mut file = fs::OpenOptions::new()
        .write(true)
        .open(file_path)
        .map_err(|e| format!("打开文件失败: {}", e))?;

    // 确保文件足够大（按需扩展）
    let current_size = file.metadata()
        .map_err(|e| format!("获取文件信息失败: {}", e))?
        .len();

    if current_size < end + 1 {
        file.set_len(end + 1)
            .map_err(|e| format!("扩展文件失败: {}", e))?;
    }

    file.seek(SeekFrom::Start(start))
        .map_err(|e| format!("文件定位失败: {}", e))?;

    // 下载数据并写入文件
    let mut stream = response.bytes_stream();
    let mut chunk_downloaded = 0u64;
    let mut last_progress_update = std::time::Instant::now();

    while let Some(chunk_result) = stream.next().await {
        let chunk = chunk_result.map_err(|e| format!("读取数据失败: {}", e))?;

        file.write_all(&chunk)
            .map_err(|e| format!("写入文件失败: {}", e))?;

        chunk_downloaded += chunk.len() as u64;

        // 更新全局下载计数
        let total_downloaded = downloaded_bytes.fetch_add(chunk.len() as u64, std::sync::atomic::Ordering::Relaxed) + chunk.len() as u64;

        // 每500ms或每1MB发送一次进度更新
        let now = std::time::Instant::now();
        if now.duration_since(last_progress_update) >= std::time::Duration::from_millis(500) ||
           chunk_downloaded >= 1024 * 1024 {

            // 计算进度百分比
            let (display_total, percentage) = if is_size_known && total_size > 0 {
                // 文件大小已知，使用实际大小计算百分比
                (total_size, (total_downloaded as f64 / total_size as f64) * 100.0)
            } else {
                // 文件大小未知，使用已下载大小作为总大小，百分比设为0直到完成
                (total_downloaded, 0.0)
            };

            // 计算下载速度
            let elapsed = now.duration_since(start_time).as_secs_f64();
            let speed = if elapsed > 0.0 {
                total_downloaded as f64 / elapsed
            } else {
                0.0
            };

            // 发送进度事件到前端
            let progress_data = serde_json::json!({
                "downloaded": total_downloaded,
                "total": display_total,
                "percentage": percentage,
                "speed": speed,
                "file_path": file_path
            });

            let _ = window.emit("download-progress", progress_data);
            last_progress_update = now;
        }

        // 防止下载超出范围
        if chunk_downloaded > (end - start + 1) {
            break;
        }
    }

    file.flush().map_err(|e| format!("刷新文件缓冲区失败: {}", e))?;

    // 只显示关键的进度信息（每100个分片显示一次）
    static CHUNK_COUNTER: std::sync::atomic::AtomicUsize = std::sync::atomic::AtomicUsize::new(0);
    let count = CHUNK_COUNTER.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
    if count % 100 == 0 {
        println!("已完成 {} 个分片下载", count + 1);
    }

    Ok(())
}

// 获取应用程序所在目录
// get_app_directory 已在前面定义，移除重复定义

// 运行百度网盘上传脚本（简化版）
#[tauri::command]
async fn run_baidu_upload_simple(
    mode: String,
    target_path: String,
    cookie: String,
    file_data: Option<Vec<u8>>,
    file_name: Option<String>,
    folder_files: Option<Vec<serde_json::Value>>
) -> Result<serde_json::Value, String> {
    use std::process::Command;
    use std::fs;
    use std::io::Write;

    println!("百度网盘上传 - 模式: {}, 目标路径: {}", mode, target_path);

    // 获取应用目录
    let app_dir = match env::current_exe() {
        Ok(exe_path) => {
            if let Some(parent) = exe_path.parent() {
                parent.to_path_buf()
            } else {
                return Err("无法获取应用目录".to_string());
            }
        }
        Err(e) => return Err(format!("获取应用目录失败: {}", e))
    };

    // 百度上传脚本路径
    let script_path = app_dir.join("baidu").join("BaiduUpload copy.py");

    if !script_path.exists() {
        return Err("百度上传脚本不存在".to_string());
    }

    // 创建临时目录用于存储文件
    let temp_dir = app_dir.join("temp_upload");
    if !temp_dir.exists() {
        fs::create_dir_all(&temp_dir).map_err(|e| format!("创建临时目录失败: {}", e))?;
    }

    // 创建cookies.json文件
    let cookies_file = temp_dir.join("cookies.json");
    let cookies_content = serde_json::json!({
        "baidu": cookie
    });
    fs::write(&cookies_file, cookies_content.to_string())
        .map_err(|e| format!("创建cookies文件失败: {}", e))?;

    let mut result = serde_json::json!({
        "success": false,
        "message": "未知错误"
    });

    match mode.as_str() {
        "test" => {
            // 测试模式：创建临时文件并上传
            let test_file = temp_dir.join("test_upload.txt");
            let test_content = format!(
                "这是一个测试上传的文件\n创建时间: {}\n百度网盘上传功能测试\n",
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
            );
            fs::write(&test_file, test_content)
                .map_err(|e| format!("创建测试文件失败: {}", e))?;

            // 执行Python脚本
            let output = Command::new("python")
                .arg(&script_path)
                .current_dir(&temp_dir)
                .env("UPLOAD_MODE", "test")
                .env("TARGET_PATH", &target_path)
                .output()
                .map_err(|e| format!("执行Python脚本失败: {}", e))?;

            // 清理测试文件
            let _ = fs::remove_file(&test_file);

            if output.status.success() {
                result = serde_json::json!({
                    "success": true,
                    "message": "测试上传成功"
                });
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                result = serde_json::json!({
                    "success": false,
                    "message": format!("测试上传失败: {}", stderr)
                });
            }
        }
        "file" => {
            // 文件上传模式
            if let (Some(data), Some(name)) = (file_data, file_name) {
                let upload_file = temp_dir.join(&name);
                fs::write(&upload_file, data)
                    .map_err(|e| format!("写入上传文件失败: {}", e))?;

                let output = Command::new("python")
                    .arg(&script_path)
                    .current_dir(&temp_dir)
                    .env("UPLOAD_MODE", "file")
                    .env("FILE_PATH", upload_file.to_string_lossy().to_string())
                    .env("TARGET_PATH", &target_path)
                    .output()
                    .map_err(|e| format!("执行Python脚本失败: {}", e))?;

                // 清理上传文件
                let _ = fs::remove_file(&upload_file);

                if output.status.success() {
                    result = serde_json::json!({
                        "success": true,
                        "message": format!("文件 {} 上传成功", name)
                    });
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    result = serde_json::json!({
                        "success": false,
                        "message": format!("文件上传失败: {}", stderr)
                    });
                }
            } else {
                result = serde_json::json!({
                    "success": false,
                    "message": "文件数据或文件名缺失"
                });
            }
        }
        "folder" => {
            // 文件夹上传模式
            if let Some(files) = folder_files {
                let folder_dir = temp_dir.join("upload_folder");
                fs::create_dir_all(&folder_dir)
                    .map_err(|e| format!("创建文件夹失败: {}", e))?;

                // 创建文件夹结构
                for file_info in &files {
                    if let (Some(path), Some(data)) = (
                        file_info.get("path").and_then(|v| v.as_str()),
                        file_info.get("data").and_then(|v| v.as_array())
                    ) {
                        let file_path = folder_dir.join(path);
                        if let Some(parent) = file_path.parent() {
                            fs::create_dir_all(parent)
                                .map_err(|e| format!("创建父目录失败: {}", e))?;
                        }

                        let bytes: Vec<u8> = data.iter()
                            .filter_map(|v| v.as_u64().map(|n| n as u8))
                            .collect();
                        fs::write(&file_path, bytes)
                            .map_err(|e| format!("写入文件失败: {}", e))?;
                    }
                }

                let output = Command::new("python")
                    .arg(&script_path)
                    .current_dir(&temp_dir)
                    .env("UPLOAD_MODE", "folder")
                    .env("FOLDER_PATH", folder_dir.to_string_lossy().to_string())
                    .env("TARGET_PATH", &target_path)
                    .output()
                    .map_err(|e| format!("执行Python脚本失败: {}", e))?;

                // 清理文件夹
                let _ = fs::remove_dir_all(&folder_dir);

                if output.status.success() {
                    result = serde_json::json!({
                        "success": true,
                        "message": format!("文件夹上传成功，共 {} 个文件", files.len())
                    });
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    result = serde_json::json!({
                        "success": false,
                        "message": format!("文件夹上传失败: {}", stderr)
                    });
                }
            } else {
                result = serde_json::json!({
                    "success": false,
                    "message": "文件夹数据缺失"
                });
            }
        }
        _ => {
            result = serde_json::json!({
                "success": false,
                "message": format!("不支持的上传模式: {}", mode)
            });
        }
    }

    // 清理cookies文件
    let _ = fs::remove_file(&cookies_file);

    Ok(result)
}



// 获取夸克下载链接
#[tauri::command]
async fn get_quark_download_url(fid: String, _cookie: String) -> Result<serde_json::Value, String> {
    // 这里应该调用现有的夸克API获取下载链接
    // 暂时返回模拟数据
    Ok(serde_json::json!({
        "success": true,
        "url": format!("https://example.com/download/{}", fid)
    }))
}

// 获取夸克文件列表
#[tauri::command]
async fn get_quark_file_list(_folder_id: String, _cookie: String) -> Result<serde_json::Value, String> {
    // 这里应该调用现有的夸克API获取文件列表
    // 暂时返回模拟数据
    Ok(serde_json::json!({
        "success": true,
        "files": []
    }))
}

// 获取百度文件列表
#[tauri::command]
async fn get_baidu_file_list(_path: String, _cookie: String) -> Result<serde_json::Value, String> {
    // 这里应该调用百度API获取文件列表
    // 暂时返回模拟数据
    Ok(serde_json::json!({
        "success": true,
        "files": []
    }))
}

// 获取百度网盘bdstoken（完全按照Python脚本实现）
#[tauri::command]
async fn get_baidu_bdstoken(cookie: String) -> Result<serde_json::Value, String> {
    println!("获取百度网盘bdstoken");

    // 创建HTTP客户端
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    // 构建URL和查询参数
    let mut url = reqwest::Url::parse("https://pan.baidu.com/api/gettemplatevariable")
        .map_err(|e| format!("解析URL失败: {}", e))?;

    url.query_pairs_mut()
        .append_pair("clienttype", "0")
        .append_pair("app_id", "250528")
        .append_pair("web", "1")
        .append_pair("fields", r#"["bdstoken","token"]"#);

    println!("bdstoken请求URL: {}", url);

    // 发送请求
    let response = client
        .get(url)
        .header("Cookie", cookie)
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
        .header("Accept", "application/json")
        .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
        .header("Cache-Control", "no-cache")
        .header("Connection", "keep-alive")
        .header("Origin", "https://pan.baidu.com")
        .header("Referer", "https://pan.baidu.com/disk/main?_at_=1694654780804")
        .send()
        .await
        .map_err(|e| format!("发送请求失败: {}", e))?;

    let status = response.status();
    let response_text = response.text().await.unwrap_or_default();

    println!("bdstoken响应: status={}, body={}", status, response_text);

    if status.is_success() {
        // 解析响应JSON
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(&response_text) {
            return Ok(json);
        }
        Err(format!("bdstoken响应解析失败: {}", response_text))
    } else {
        Err(format!("bdstoken请求失败: HTTP {}, 响应: {}", status, response_text))
    }
}







#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            http_request,
            quark_qr_get_token,
            quark_qr_check_status,
            file_exists,
            get_file_size,
            calculate_file_md5,
            read_file_bytes,
            get_all_files_in_folder,
            create_test_folder_structure,
            create_dir_all,
            read_file,
            write_file,
            read_text_file,
            write_text_file,
            delete_file,
            get_temp_dir,
            list_directory,
            get_app_directory,
            download_file,
            download_file_with_progress,
            download_file_chunked,
            run_baidu_upload_simple,
            get_baidu_bdstoken,
            get_quark_download_url,
            get_quark_file_list,
            get_baidu_file_list
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
