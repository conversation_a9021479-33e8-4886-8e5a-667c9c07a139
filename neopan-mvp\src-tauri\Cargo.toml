[package]
name = "neopan"
version = "2.4.3"
description = "NeoPan - 网盘批处理助手"
authors = ["NeoPan Team"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "neopan_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-fs = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.11", features = ["json", "gzip", "deflate", "brotli", "stream", "multipart"] }
tokio = { version = "1", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
lazy_static = "1.4"
futures-util = "0.3"
url = "2.4"
md5 = "0.7"
urlencoding = "2.1"
chrono = { version = "0.4", features = ["serde"] }

