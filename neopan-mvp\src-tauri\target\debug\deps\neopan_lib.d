C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\deps\neopan_lib.lib: src\lib.rs C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\build\neopan-8b6a97824d7457c6\out/25736134b9a60173a4ea68e8eaf5e9124aa1b83870e454c0ab8d90cebad9e17a

C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\deps\neopan_lib.dll: src\lib.rs C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\build\neopan-8b6a97824d7457c6\out/25736134b9a60173a4ea68e8eaf5e9124aa1b83870e454c0ab8d90cebad9e17a

C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\deps\libneopan_lib.rlib: src\lib.rs C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\build\neopan-8b6a97824d7457c6\out/25736134b9a60173a4ea68e8eaf5e9124aa1b83870e454c0ab8d90cebad9e17a

C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\deps\neopan_lib.d: src\lib.rs C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\build\neopan-8b6a97824d7457c6\out/25736134b9a60173a4ea68e8eaf5e9124aa1b83870e454c0ab8d90cebad9e17a

src\lib.rs:
C:\Users\<USER>\Documents\GitHub\neopan\neopan-mvp\src-tauri\target\debug\build\neopan-8b6a97824d7457c6\out/25736134b9a60173a4ea68e8eaf5e9124aa1b83870e454c0ab8d90cebad9e17a:

# env-dep:CARGO_PKG_AUTHORS=NeoPan Team
# env-dep:CARGO_PKG_DESCRIPTION=NeoPan - 网盘批处理助手
# env-dep:CARGO_PKG_NAME=neopan
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Documents\\GitHub\\neopan\\neopan-mvp\\src-tauri\\target\\debug\\build\\neopan-8b6a97824d7457c6\\out
