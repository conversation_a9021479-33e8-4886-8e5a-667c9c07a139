<template>
  <div class="baidu-upload-simple">
    <div class="header">
      <h2>百度网盘上传测试</h2>
      <p class="description">基于 BaiduUpload copy.py 的简化上传功能</p>
    </div>

    <!-- 消息显示 -->
    <div v-if="message" :class="['message', `message-${messageType}`]">
      {{ message }}
    </div>

    <!-- Cookie 配置 -->
    <div class="section">
      <h3>Cookie 配置</h3>
      <div class="form-group">
        <label>百度网盘 Cookie:</label>
        <textarea
          v-model="baiduCookie"
          placeholder="请输入百度网盘的Cookie字符串"
          rows="3"
          class="cookie-input"
        ></textarea>
      </div>
    </div>

    <!-- 上传选项 -->
    <div class="section">
      <h3>上传选项</h3>
      <div class="upload-options">
        <div class="option-group">
          <label>
            <input type="radio" v-model="uploadMode" value="test" />
            测试上传（创建临时文件）
          </label>
        </div>
        <div class="option-group">
          <label>
            <input type="radio" v-model="uploadMode" value="file" />
            上传指定文件
          </label>
        </div>
        <div class="option-group">
          <label>
            <input type="radio" v-model="uploadMode" value="folder" />
            上传文件夹（保持结构）
          </label>
        </div>
      </div>
    </div>

    <!-- 文件选择 -->
    <div v-if="uploadMode === 'file'" class="section">
      <h3>选择文件</h3>
      <input
        type="file"
        @change="handleFileSelect"
        class="file-input"
      />
      <div v-if="selectedFile" class="file-info">
        <p><strong>文件名:</strong> {{ selectedFile.name }}</p>
        <p><strong>文件大小:</strong> {{ formatFileSize(selectedFile.size) }}</p>
      </div>
    </div>

    <!-- 文件夹选择 -->
    <div v-if="uploadMode === 'folder'" class="section">
      <h3>选择文件夹</h3>
      <input
        type="file"
        webkitdirectory
        @change="handleFolderSelect"
        class="file-input"
      />
      <div v-if="selectedFiles.length > 0" class="folder-info">
        <p><strong>文件数量:</strong> {{ selectedFiles.length }}</p>
        <p><strong>总大小:</strong> {{ formatFileSize(totalSize) }}</p>
      </div>
    </div>

    <!-- 目标路径 -->
    <div class="section">
      <h3>目标路径</h3>
      <div class="path-options">
        <label>
          <input type="radio" v-model="targetPath" value="/" />
          根目录 (/)
        </label>
        <label>
          <input type="radio" v-model="targetPath" value="/我的文件/" />
          我的文件 (/我的文件/)
        </label>
        <label>
          <input type="radio" v-model="targetPath" value="custom" />
          自定义路径
        </label>
      </div>
      <div v-if="targetPath === 'custom'" class="custom-path">
        <input
          v-model="customPath"
          type="text"
          placeholder="请输入自定义路径，如: /测试文件夹/"
          class="path-input"
        />
      </div>
    </div>

    <!-- 上传按钮 -->
    <div class="section">
      <button
        @click="startUpload"
        :disabled="!canUpload || uploading"
        class="upload-btn"
        :class="{ uploading }"
      >
        <i :class="uploading ? 'fas fa-spinner fa-spin' : 'fas fa-upload'"></i>
        {{ uploading ? '上传中...' : '开始上传' }}
      </button>
    </div>

    <!-- 进度显示 -->
    <div v-if="uploading" class="progress-section">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      <p class="progress-text">{{ progressText }}</p>
    </div>

    <!-- 日志显示 -->
    <div v-if="logs.length > 0" class="logs-section">
      <h3>上传日志</h3>
      <div class="logs">
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 响应式数据
const baiduCookie = ref('')
const uploadMode = ref('test')
const selectedFile = ref<File | null>(null)
const selectedFiles = ref<File[]>([])
const targetPath = ref('/')
const customPath = ref('')
const uploading = ref(false)
const progress = ref(0)
const progressText = ref('')
const message = ref('')
const messageType = ref<'success' | 'error' | 'info'>('info')
const logs = ref<Array<{ time: string; message: string; type: string }>>([])

// 计算属性
const canUpload = computed(() => {
  if (!baiduCookie.value.trim()) return false
  
  switch (uploadMode.value) {
    case 'test':
      return true
    case 'file':
      return selectedFile.value !== null
    case 'folder':
      return selectedFiles.value.length > 0
    default:
      return false
  }
})

const totalSize = computed(() => {
  return selectedFiles.value.reduce((sum, file) => sum + file.size, 0)
})

const finalTargetPath = computed(() => {
  if (targetPath.value === 'custom') {
    let path = customPath.value.trim()
    if (!path.startsWith('/')) path = '/' + path
    if (!path.endsWith('/')) path += '/'
    return path
  }
  return targetPath.value
})

// 方法
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0]
  }
}

const handleFolderSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    selectedFiles.value = Array.from(target.files)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const addLog = (message: string, type: string = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.push({ time, message, type })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-100)
  }
}

const showMessage = (msg: string, type: 'success' | 'error' | 'info' = 'info') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

const startUpload = async () => {
  if (!canUpload.value) return
  
  uploading.value = true
  progress.value = 0
  progressText.value = '准备上传...'
  logs.value = []
  
  try {
    addLog('开始上传流程', 'info')
    
    // 调用 Python 脚本进行上传
    const result = await invoke('run_baidu_upload_simple', {
      mode: uploadMode.value,
      targetPath: finalTargetPath.value,
      cookie: baiduCookie.value.trim(),
      fileData: uploadMode.value === 'file' && selectedFile.value ? 
        await fileToBytes(selectedFile.value) : null,
      fileName: uploadMode.value === 'file' && selectedFile.value ? 
        selectedFile.value.name : null,
      folderFiles: uploadMode.value === 'folder' ? 
        await processFolder() : null
    })
    
    if (result.success) {
      progress.value = 100
      progressText.value = '上传完成'
      addLog('上传成功', 'success')
      showMessage('上传成功！', 'success')
    } else {
      addLog(`上传失败: ${result.message}`, 'error')
      showMessage(`上传失败: ${result.message}`, 'error')
    }
    
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    addLog(`上传出错: ${errorMsg}`, 'error')
    showMessage(`上传出错: ${errorMsg}`, 'error')
  } finally {
    uploading.value = false
  }
}

const fileToBytes = async (file: File): Promise<number[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const arrayBuffer = reader.result as ArrayBuffer
      const uint8Array = new Uint8Array(arrayBuffer)
      resolve(Array.from(uint8Array))
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

const processFolder = async () => {
  const folderData = []
  for (const file of selectedFiles.value) {
    const bytes = await fileToBytes(file)
    folderData.push({
      path: file.webkitRelativePath,
      name: file.name,
      size: file.size,
      data: bytes
    })
  }
  return folderData
}
</script>

<style scoped>
.baidu-upload-simple {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
}

.description {
  color: #666;
  font-size: 14px;
}

.section {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.message {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
}

.message-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.cookie-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
}

.upload-options .option-group {
  margin-bottom: 10px;
}

.upload-options label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.upload-options input[type="radio"] {
  margin-right: 8px;
}

.file-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.file-info, .folder-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
}

.path-options label {
  display: block;
  margin-bottom: 8px;
  cursor: pointer;
}

.path-options input[type="radio"] {
  margin-right: 8px;
}

.custom-path {
  margin-top: 10px;
}

.path-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.upload-btn {
  width: 100%;
  padding: 12px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.upload-btn:hover:not(:disabled) {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.upload-btn.uploading {
  background: #28a745;
}

.progress-section {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #28a745;
  transition: width 0.3s;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #666;
}

.logs-section {
  margin-top: 30px;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-success {
  background: #d4edda;
  color: #155724;
}

.log-error {
  background: #f8d7da;
  color: #721c24;
}

.log-info {
  color: #495057;
}
</style>
