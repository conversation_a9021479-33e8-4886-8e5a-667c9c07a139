<template>
  <div class="baidu-upload-complete">
    <div class="header">
      <h2>🌟 百度网盘上传工具</h2>
      <p class="description">完全集成 BaiduUpload copy.py 的所有功能</p>
    </div>

    <!-- 消息显示 -->
    <div v-if="message" :class="['message', `message-${messageType}`]">
      {{ message }}
    </div>

    <!-- Cookie 配置 -->
    <div class="section">
      <h3>🔑 Cookie 配置</h3>
      <div class="form-group">
        <label>百度网盘 Cookie:</label>
        <textarea
          v-model="baiduCookie"
          placeholder="请输入百度网盘的Cookie字符串（从浏览器开发者工具中获取）"
          rows="4"
          class="cookie-input"
        ></textarea>
        <div class="cookie-help">
          <p>💡 获取Cookie方法：</p>
          <ol>
            <li>在浏览器中登录百度网盘</li>
            <li>按F12打开开发者工具</li>
            <li>在Network标签页中刷新页面</li>
            <li>找到任意请求，复制Cookie值</li>
          </ol>
        </div>
      </div>
    </div>

    <!-- 主要功能选择 -->
    <div class="section">
      <h3>📋 选择操作</h3>
      <div class="main-options">
        <div class="option-card" :class="{ active: selectedOption === 'test' }" @click="selectedOption = 'test'">
          <div class="option-icon">🧪</div>
          <div class="option-title">测试上传</div>
          <div class="option-desc">创建临时文件进行上传测试</div>
        </div>
        <div class="option-card" :class="{ active: selectedOption === 'file' }" @click="selectedOption = 'file'">
          <div class="option-icon">📄</div>
          <div class="option-title">上传指定文件</div>
          <div class="option-desc">选择单个文件进行上传</div>
        </div>
        <div class="option-card" :class="{ active: selectedOption === 'folder' }" @click="selectedOption = 'folder'">
          <div class="option-icon">📁</div>
          <div class="option-title">上传文件夹</div>
          <div class="option-desc">保持完整文件夹结构上传</div>
        </div>
      </div>
    </div>

    <!-- 文件选择区域 -->
    <div v-if="selectedOption === 'file'" class="section">
      <h3>📄 选择文件</h3>
      <div class="file-drop-zone" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
        <input
          type="file"
          ref="fileInput"
          @change="handleFileSelect"
          class="file-input-hidden"
          id="file-input"
        />
        <label for="file-input" class="file-drop-label">
          <div class="drop-icon">📁</div>
          <div class="drop-text">
            <p>点击选择文件或拖拽文件到此处</p>
            <p class="drop-hint">支持任意格式的文件</p>
          </div>
        </label>
      </div>
      <div v-if="selectedFile" class="file-info">
        <div class="file-item">
          <div class="file-icon">📄</div>
          <div class="file-details">
            <div class="file-name">{{ selectedFile.name }}</div>
            <div class="file-size">{{ formatFileSize(selectedFile.size) }}</div>
            <div class="file-path">{{ selectedFile.webkitRelativePath || '单个文件' }}</div>
          </div>
          <button @click="clearFileSelection" class="remove-btn">❌</button>
        </div>
      </div>
    </div>

    <!-- 文件夹选择区域 -->
    <div v-if="selectedOption === 'folder'" class="section">
      <h3>📁 选择文件夹</h3>
      <div class="folder-drop-zone">
        <input
          type="file"
          ref="folderInput"
          webkitdirectory
          @change="handleFolderSelect"
          class="file-input-hidden"
          id="folder-input"
        />
        <label for="folder-input" class="folder-drop-label">
          <div class="drop-icon">📁</div>
          <div class="drop-text">
            <p>点击选择文件夹</p>
            <p class="drop-hint">将保持完整的文件夹层级结构</p>
          </div>
        </label>
      </div>
      <div v-if="selectedFiles.length > 0" class="folder-info">
        <div class="folder-summary">
          <h4>📊 文件夹信息</h4>
          <p><strong>文件数量:</strong> {{ selectedFiles.length }}</p>
          <p><strong>总大小:</strong> {{ formatFileSize(totalSize) }}</p>
          <p><strong>文件夹名:</strong> {{ folderName }}</p>
        </div>
        <div class="file-list">
          <h4>📋 文件列表 (显示前10个)</h4>
          <div class="file-list-container">
            <div
              v-for="(file, index) in selectedFiles.slice(0, 10)"
              :key="index"
              class="file-item"
            >
              <div class="file-icon">📄</div>
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size) }}</div>
                <div class="file-path">{{ file.webkitRelativePath }}</div>
              </div>
            </div>
            <div v-if="selectedFiles.length > 10" class="more-files">
              ... 还有 {{ selectedFiles.length - 10 }} 个文件
            </div>
          </div>
        </div>
        <button @click="clearFolderSelection" class="clear-btn">清除选择</button>
      </div>
    </div>

    <!-- 目标路径配置 -->
    <div class="section">
      <h3>🎯 目标路径</h3>
      <div class="path-config">
        <div class="path-presets">
          <button
            v-for="preset in pathPresets"
            :key="preset.value"
            @click="targetPath = preset.value"
            :class="['path-preset', { active: targetPath === preset.value }]"
          >
            {{ preset.label }}
          </button>
        </div>
        <div class="custom-path-input">
          <label>自定义路径:</label>
          <input
            v-model="targetPath"
            type="text"
            placeholder="例如: /我的文件/上传测试/"
            class="path-input"
          />
          <div class="path-help">
            💡 路径必须以 / 开头和结尾，例如: /测试文件夹/
          </div>
        </div>
      </div>
    </div>

    <!-- 上传选项 -->
    <div class="section">
      <h3>⚙️ 上传选项</h3>
      <div class="upload-options">
        <label class="option-checkbox">
          <input type="checkbox" v-model="overwriteExisting" />
          <span class="checkmark"></span>
          覆盖已存在的文件
        </label>
        <label class="option-checkbox">
          <input type="checkbox" v-model="showDetailedLog" />
          <span class="checkmark"></span>
          显示详细日志
        </label>
      </div>
    </div>

    <!-- 上传按钮 -->
    <div class="section">
      <button
        @click="startUpload"
        :disabled="!canUpload || uploading"
        class="upload-btn"
        :class="{ uploading, disabled: !canUpload }"
      >
        <i :class="uploading ? 'fas fa-spinner fa-spin' : 'fas fa-upload'"></i>
        <span>{{ getUploadButtonText() }}</span>
      </button>
      <div v-if="!canUpload" class="upload-requirements">
        <p>❌ 上传要求:</p>
        <ul>
          <li v-if="!baiduCookie.trim()">请输入百度网盘Cookie</li>
          <li v-if="!selectedOption">请选择上传类型</li>
          <li v-if="selectedOption === 'file' && !selectedFile">请选择要上传的文件</li>
          <li v-if="selectedOption === 'folder' && selectedFiles.length === 0">请选择要上传的文件夹</li>
          <li v-if="!targetPath.trim()">请设置目标路径</li>
        </ul>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading || uploadComplete" class="progress-section">
      <h3>📊 上传进度</h3>
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
          <div class="progress-text">{{ progress.toFixed(1) }}%</div>
        </div>
        <div class="progress-details">
          <p class="progress-status">{{ progressText }}</p>
          <div v-if="currentFile" class="current-file">
            <span>当前文件: {{ currentFile }}</span>
          </div>
          <div v-if="uploadStats.total > 0" class="upload-stats">
            <span>进度: {{ uploadStats.completed }}/{{ uploadStats.total }}</span>
            <span v-if="uploadStats.failed > 0" class="failed-count">失败: {{ uploadStats.failed }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细日志 -->
    <div v-if="logs.length > 0" class="logs-section">
      <div class="logs-header">
        <h3>📝 上传日志</h3>
        <div class="logs-controls">
          <button @click="clearLogs" class="clear-logs-btn">清除日志</button>
          <button @click="exportLogs" class="export-logs-btn">导出日志</button>
        </div>
      </div>
      <div class="logs-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- 上传结果 -->
    <div v-if="uploadComplete" class="results-section">
      <h3>✅ 上传完成</h3>
      <div class="results-summary">
        <div class="result-item">
          <span class="result-label">总文件数:</span>
          <span class="result-value">{{ uploadStats.total }}</span>
        </div>
        <div class="result-item">
          <span class="result-label">成功上传:</span>
          <span class="result-value success">{{ uploadStats.completed }}</span>
        </div>
        <div v-if="uploadStats.failed > 0" class="result-item">
          <span class="result-label">上传失败:</span>
          <span class="result-value error">{{ uploadStats.failed }}</span>
        </div>
        <div class="result-item">
          <span class="result-label">总用时:</span>
          <span class="result-value">{{ formatDuration(uploadDuration) }}</span>
        </div>
      </div>
      <div class="results-actions">
        <button @click="resetUpload" class="reset-btn">重新上传</button>
        <button @click="openBaiduPan" class="open-baidu-btn">打开百度网盘</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import { open } from '@tauri-apps/api/shell'

// 响应式数据
const baiduCookie = ref('')
const selectedOption = ref('')
const selectedFile = ref<File | null>(null)
const selectedFiles = ref<File[]>([])
const targetPath = ref('/')
const overwriteExisting = ref(false)
const showDetailedLog = ref(true)
const uploading = ref(false)
const uploadComplete = ref(false)
const progress = ref(0)
const progressText = ref('')
const currentFile = ref('')
const message = ref('')
const messageType = ref<'success' | 'error' | 'info'>('info')
const logs = ref<Array<{ time: string; level: string; message: string; type: string }>>([])
const uploadStats = ref({
  total: 0,
  completed: 0,
  failed: 0
})
const uploadDuration = ref(0)
const uploadStartTime = ref(0)

// 路径预设
const pathPresets = ref([
  { label: '根目录', value: '/' },
  { label: '我的文件', value: '/我的文件/' },
  { label: '测试文件夹', value: '/测试文件夹/' },
  { label: '上传测试', value: '/上传测试/' }
])

// 计算属性
const canUpload = computed(() => {
  if (!baiduCookie.value.trim()) return false
  if (!selectedOption.value) return false
  if (!targetPath.value.trim()) return false

  switch (selectedOption.value) {
    case 'test':
      return true
    case 'file':
      return selectedFile.value !== null
    case 'folder':
      return selectedFiles.value.length > 0
    default:
      return false
  }
})

const totalSize = computed(() => {
  return selectedFiles.value.reduce((sum, file) => sum + file.size, 0)
})

const folderName = computed(() => {
  if (selectedFiles.value.length === 0) return ''
  const firstFile = selectedFiles.value[0]
  const parts = firstFile.webkitRelativePath.split('/')
  return parts[0] || ''
})

const finalTargetPath = computed(() => {
  let path = targetPath.value.trim()
  if (!path.startsWith('/')) path = '/' + path
  if (!path.endsWith('/')) path += '/'
  return path
})

// 方法
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0]
    addLog(`选择文件: ${target.files[0].name} (${formatFileSize(target.files[0].size)})`, 'info')
  }
}

const handleFolderSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    selectedFiles.value = Array.from(target.files)
    addLog(`选择文件夹: ${folderName.value}, 包含 ${target.files.length} 个文件`, 'info')
  }
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    selectedFile.value = files[0]
    addLog(`拖拽文件: ${files[0].name} (${formatFileSize(files[0].size)})`, 'info')
  }
}

const clearFileSelection = () => {
  selectedFile.value = null
  addLog('清除文件选择', 'info')
}

const clearFolderSelection = () => {
  selectedFiles.value = []
  addLog('清除文件夹选择', 'info')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds.toFixed(1)}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds.toFixed(1)}秒`
}

const addLog = (message: string, type: string = 'info', level: string = 'INFO') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.push({ time, level, message, type })

  // 限制日志数量
  if (logs.value.length > 1000) {
    logs.value = logs.value.slice(-1000)
  }

  // 自动滚动到底部
  setTimeout(() => {
    const logsContainer = document.querySelector('.logs-container')
    if (logsContainer) {
      logsContainer.scrollTop = logsContainer.scrollHeight
    }
  }, 100)
}

const showMessage = (msg: string, type: 'success' | 'error' | 'info' = 'info') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

const getUploadButtonText = (): string => {
  if (uploading.value) return '上传中...'
  if (!canUpload.value) return '请完成配置'

  switch (selectedOption.value) {
    case 'test':
      return '开始测试上传'
    case 'file':
      return `上传文件: ${selectedFile.value?.name || ''}`
    case 'folder':
      return `上传文件夹: ${folderName.value} (${selectedFiles.value.length}个文件)`
    default:
      return '开始上传'
  }
}

const startUpload = async () => {
  if (!canUpload.value) return

  // 重置状态
  uploading.value = true
  uploadComplete.value = false
  progress.value = 0
  progressText.value = '初始化上传...'
  currentFile.value = ''
  uploadStats.value = { total: 0, completed: 0, failed: 0 }
  uploadStartTime.value = Date.now()

  addLog('🚀 开始上传流程', 'info', 'START')
  addLog(`📋 上传模式: ${selectedOption.value}`, 'info', 'CONFIG')
  addLog(`🎯 目标路径: ${finalTargetPath.value}`, 'info', 'CONFIG')

  try {
    let result

    switch (selectedOption.value) {
      case 'test':
        result = await performTestUpload()
        break
      case 'file':
        result = await performFileUpload()
        break
      case 'folder':
        result = await performFolderUpload()
        break
      default:
        throw new Error('未知的上传模式')
    }

    uploadDuration.value = (Date.now() - uploadStartTime.value) / 1000

    if (result.success) {
      progress.value = 100
      progressText.value = '✅ 上传完成'
      uploadComplete.value = true
      addLog('🎉 上传成功完成', 'success', 'SUCCESS')
      showMessage('上传成功！', 'success')
    } else {
      addLog(`❌ 上传失败: ${result.message}`, 'error', 'ERROR')
      showMessage(`上传失败: ${result.message}`, 'error')
    }

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    addLog(`💥 上传出错: ${errorMsg}`, 'error', 'ERROR')
    showMessage(`上传出错: ${errorMsg}`, 'error')
  } finally {
    uploading.value = false
  }
}

const performTestUpload = async () => {
  progressText.value = '🧪 执行测试上传...'
  uploadStats.value.total = 1

  const result = await invoke('run_baidu_upload_complete', {
    mode: 'test',
    targetPath: finalTargetPath.value,
    cookie: baiduCookie.value.trim(),
    overwrite: overwriteExisting.value
  })

  if (result.success) {
    uploadStats.value.completed = 1
    progress.value = 100
  } else {
    uploadStats.value.failed = 1
  }

  return result
}

const performFileUpload = async () => {
  if (!selectedFile.value) throw new Error('未选择文件')

  progressText.value = `📄 上传文件: ${selectedFile.value.name}`
  currentFile.value = selectedFile.value.name
  uploadStats.value.total = 1

  // 使用Tauri的文件系统API保存文件到临时位置
  const result = await invoke('run_baidu_upload_complete', {
    mode: 'file',
    targetPath: finalTargetPath.value,
    cookie: baiduCookie.value.trim(),
    overwrite: overwriteExisting.value,
    fileName: selectedFile.value.name,
    fileSize: selectedFile.value.size
  })

  if (result.success) {
    uploadStats.value.completed = 1
    progress.value = 100
  } else {
    uploadStats.value.failed = 1
  }

  return result
}

const performFolderUpload = async () => {
  if (selectedFiles.value.length === 0) throw new Error('未选择文件夹')

  progressText.value = `📁 上传文件夹: ${folderName.value}`
  uploadStats.value.total = selectedFiles.value.length

  // 批量上传文件夹中的所有文件
  const result = await invoke('run_baidu_upload_complete', {
    mode: 'folder',
    targetPath: finalTargetPath.value,
    cookie: baiduCookie.value.trim(),
    overwrite: overwriteExisting.value,
    folderName: folderName.value,
    fileCount: selectedFiles.value.length,
    totalSize: totalSize.value
  })

  if (result.success) {
    uploadStats.value.completed = selectedFiles.value.length
    progress.value = 100
  } else {
    uploadStats.value.failed = selectedFiles.value.length
  }

  return result
}
const clearLogs = () => {
  logs.value = []
  addLog('📝 日志已清除', 'info', 'SYSTEM')
}

const exportLogs = () => {
  const logText = logs.value.map(log =>
    `[${log.time}] ${log.level}: ${log.message}`
  ).join('\n')

  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `baidu-upload-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  addLog('📋 日志已导出', 'info', 'SYSTEM')
}

const resetUpload = () => {
  uploading.value = false
  uploadComplete.value = false
  progress.value = 0
  progressText.value = ''
  currentFile.value = ''
  uploadStats.value = { total: 0, completed: 0, failed: 0 }
  uploadDuration.value = 0
  addLog('🔄 重置上传状态', 'info', 'SYSTEM')
}

const openBaiduPan = async () => {
  try {
    await open('https://pan.baidu.com')
    addLog('🌐 已打开百度网盘', 'info', 'SYSTEM')
  } catch (error) {
    addLog('❌ 无法打开百度网盘', 'error', 'ERROR')
  }
}

// 组件挂载时的初始化
onMounted(() => {
  addLog('🎯 百度网盘上传工具已加载', 'info', 'SYSTEM')

  // 尝试从本地存储恢复Cookie
  const savedCookie = localStorage.getItem('baidu-cookie')
  if (savedCookie) {
    baiduCookie.value = savedCookie
    addLog('🔑 已恢复保存的Cookie', 'info', 'SYSTEM')
  }
})

// 监听Cookie变化并保存到本地存储
const saveCookie = () => {
  if (baiduCookie.value.trim()) {
    localStorage.setItem('baidu-cookie', baiduCookie.value.trim())
    addLog('💾 Cookie已保存', 'info', 'SYSTEM')
  }
}
</script>

<style scoped>
.baidu-upload-simple {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
}

.description {
  color: #666;
  font-size: 14px;
}

.section {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.message {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
}

.message-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.cookie-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
}

.upload-options .option-group {
  margin-bottom: 10px;
}

.upload-options label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.upload-options input[type="radio"] {
  margin-right: 8px;
}

.file-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.file-info, .folder-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
}

.path-options label {
  display: block;
  margin-bottom: 8px;
  cursor: pointer;
}

.path-options input[type="radio"] {
  margin-right: 8px;
}

.custom-path {
  margin-top: 10px;
}

.path-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.upload-btn {
  width: 100%;
  padding: 12px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.upload-btn:hover:not(:disabled) {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.upload-btn.uploading {
  background: #28a745;
}

.progress-section {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #28a745;
  transition: width 0.3s;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #666;
}

.logs-section {
  margin-top: 30px;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-success {
  background: #d4edda;
  color: #155724;
}

.log-error {
  background: #f8d7da;
  color: #721c24;
}

.log-info {
  color: #495057;
}
</style>
